{"optimization_mode": "separate", "datasets": ["rml201801a"], "n_trials_per_dataset": 30, "total_trials": 30, "study_name": "wnn_mrnn_hyperopt", "optimization_config": {"study_name": "wnn_mrnn_hyperopt", "config_path": "config.yaml", "datasets": ["rml201801a"], "optimization_mode": "separate", "training_epochs": 1, "early_stop_patience": 1, "min_epochs": 1, "n_trials_per_dataset": 30, "optimize_params": ["wavelet_dim", "rnn_dim", "num_levels", "num_layers", "dropout"], "skip_on_oom": true, "max_oom_retries": 1, "save_frequency": 1, "enable_database": true, "database_url": "sqlite:///wnn_mrnn_optimization.db"}, "completion_time": "2025-06-25T09:22:58.959986", "best_results_by_dataset": {"rml201801a": {"trial_number": 15, "timestamp": "2025-06-25T02:16:25.904065", "best_params": {"wavelet_dim": 128, "rnn_dim": 128, "num_levels": 2, "num_layers": 4, "dropout": 0.11814628140835459}, "best_value": 0.5795829790341849, "dataset": "rml201801a", "total_trials_so_far": 14}}}