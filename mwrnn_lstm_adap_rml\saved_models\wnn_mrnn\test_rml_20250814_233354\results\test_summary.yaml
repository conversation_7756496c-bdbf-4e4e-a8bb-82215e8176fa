dataset_info:
  dataset_type: rml
  input_shape: !!python/tuple
  - 2
  - 128
  num_classes: 11
  snr_range:
  - -20.0
  - 18.0
  total_samples: 41800
inference_performance:
  avg_inference_time_ms: 0.06789054026443993
  max_inference_time_ms: 2.8356313705444336
  min_inference_time_ms: 0.059820711612701416
  std_inference_time_ms: 0.043901197242231224
model_complexity:
  macs: 18.466M
  macs_raw: 18465728.0
  parameters: 230.603K
  params_raw: 230603.0
overall_metrics:
  accuracy: 62.95454545454545
  kappa: 0.5925
  macro_f1: 65.25029137552872
test_info:
  config_path: config.yaml
  model_path: ./saved_models/wnn_mrnn/rml_20250814_191526/models/best_model.pth
  test_date: '2025-08-14 23:34:22'
