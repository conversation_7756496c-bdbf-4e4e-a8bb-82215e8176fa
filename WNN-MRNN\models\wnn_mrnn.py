import torch
import torch.nn as nn
import torch.nn.functional as F
from .wavelet import WaveletDecomposition
from .vmrnn_b import MMRNNClassifier  # 导入新设计的MMRNNClassifier

# 从mamba_simple.py导入Mamba
from mamba_ssm.modules.mamba_simple import Mamba




class WNN_MRNN(nn.Module):
    """WNN-MRNN模型，使用MMRNNClassifier处理小波分解后的特征进行分类"""
    def __init__(self, in_channels=2, num_classes=11, 
                 wavelet_dim=64, rnn_dim=64, 
                 num_layers=2, num_levels=3, d_state=16, 
                 msb_depth=1, drop_rate=0.1, d_conv=4, expand=2):
        """
        初始化WNN-MRNN模型
        
        Args:
            in_channels: 输入通道数（如I/Q为2）
            num_classes: 分类类别数
            wavelet_dim: 小波分解输出通道数
            rnn_dim: RNN隐藏层维度
            num_layers: MMRNNCell的层数
            num_levels: 小波分解层数
            d_state: 状态空间维度
            msb_depth: MSB内部深度，控制每个MSB块内部的复杂度
            drop_rate: Dropout比率
            d_conv: Mamba卷积核大小
            expand: Mamba扩展因子
        """
        super(WNN_MRNN, self).__init__()
        
        self.in_channels = in_channels
        self.num_classes = num_classes
        self.num_levels = num_levels
        self.wavelet_dim = wavelet_dim
        self.rnn_dim = rnn_dim
        
        # 从AWN模型添加的初始卷积层
        # 1. 2D卷积，处理IQ通道
        self.conv1 = nn.Conv2d(1, 64, kernel_size=(2, 7), padding=(0, 3))
        self.bn1 = nn.BatchNorm2d(64)
        
        # 2. 1D卷积序列
        self.conv2 = nn.Conv1d(64, 64, kernel_size=5, padding=2)
        self.bn2 = nn.BatchNorm1d(64)
        
        self.conv3 = nn.Conv1d(64, 64, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm1d(64)
        
        self.relu = nn.ReLU(inplace=True)
        
        # 小波分解
        self.wavelet = WaveletDecomposition(channels=64, decomposition_levels=num_levels)
        
        # 创建特征提取器，为每个分量创建独立的特征提取器，保持相同的输出维度
        self.conv_low = nn.Sequential(
            nn.Conv1d(64, wavelet_dim, kernel_size=3, padding=1),
            nn.BatchNorm1d(wavelet_dim),
            nn.ReLU(inplace=True)
        )
        
        self.conv_high_list = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(64, wavelet_dim, kernel_size=3, padding=1),
                nn.BatchNorm1d(wavelet_dim),
                nn.ReLU(inplace=True)
            )
            for _ in range(num_levels)
        ])
        
        # MMRNNClassifier用于处理小波分量序列
        self.mmrnn_classifier = MMRNNClassifier(
            input_dim=wavelet_dim,     # 输入特征维度
            hidden_dim=rnn_dim,        # RNN隐藏层维度
            num_components=num_levels+1,  # 分量数量 = 高频分量数量 + 1个低频分量
            num_layers=num_layers,     # MMRNNCell层数
            num_classes=num_classes,   # 分类类别数
            d_state=d_state,           # 状态空间维度
            dropout=drop_rate,         # dropout比率
            msb_depth=msb_depth,       # MSB内部深度
            d_conv=d_conv,             # Mamba卷积核大小
            expand=expand              # Mamba扩展因子
        )
    
    def _process_initial_features(self, x):
        """
        使用初始卷积层处理输入信号
        
        Args:
            x: 输入信号 [B, 2, L]，其中2表示I/Q通道
            
        Returns:
            x: 处理后的特征 [B, 64, L]
        """
        # 如有必要，添加通道维度: [B, 2, L] -> [B, 1, 2, L]
        if x.dim() == 3:
            x = x.unsqueeze(1)
            
        # 2D卷积处理IQ通道
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        
        # 去除多余维度: [B, 64, 1, L] -> [B, 64, L]
        x = x.squeeze(2)
        
        # 1D卷积序列
        x = self.conv2(x)
        x = self.bn2(x)
        x = self.relu(x)
        
        x = self.conv3(x)
        x = self.bn3(x)
        x = self.relu(x)
        
        return x
        
    def _process_input(self, x):
        """
        处理输入数据，执行小波分解并提取特征
        
        Args:
            x: 输入特征 [B, 64, L]，已经经过初始卷积层处理
        
        Returns:
            high_features_list: 高频特征列表，每个元素形状为 [B, wavelet_dim, L/2^i]
            low_features: 低频特征，形状为 [B, wavelet_dim, L/2^num_levels]
        """
        # 小波分解 - 返回高频分量列表和一个低频分量
        high_list, low = self.wavelet(x)
        
        # 对低频分量应用卷积特征提取
        low_features = self.conv_low(low)
        
        # 对每个高频分量应用卷积特征提取
        high_features_list = []
        for i, high in enumerate(high_list):
            high_features = self.conv_high_list[i](high)
            high_features_list.append(high_features)
        
        return high_features_list, low_features
        
    def _prepare_wavelet_components_for_mrnn(self, high_features_list, low_features):
        """
        准备小波分量用于MRNN处理，确保所有分量具有相同的维度
        
        Args:
            high_features_list: 高频特征列表
            low_features: 低频特征
            
        Returns:
            components_sequence: 调整后的分量序列 [B, num_components, seq_len, wavelet_dim]
        """
        batch_size = high_features_list[0].size(0)
        components = []
        
        # 处理高频分量，调整每个分量的尺寸以匹配第一个高频分量
        reference_length = high_features_list[0].size(2)  # 参考长度，使用第一个高频分量
        
        # 首先处理所有高频分量，按顺序：高频1, 高频2, 高频3
        for high_features in high_features_list:
            current_length = high_features.size(2)
            
            # 如果长度不同，调整为参考长度
            if current_length != reference_length:
                # 使用插值调整大小
                high_features = F.interpolate(
                    high_features, 
                    size=reference_length,
                    mode='linear',
                    align_corners=False
                )
            
            # 调整维度 [B, wavelet_dim, seq_len] -> [B, seq_len, wavelet_dim]
            high_features = high_features.permute(0, 2, 1)
            components.append(high_features)
        
        # 处理低频分量
        low_length = low_features.size(2)
        if low_length != reference_length:
            # 调整低频分量
            low_features = F.interpolate(
                low_features,
                size=reference_length,
                mode='linear',
                align_corners=False
            )
        
        # 调整维度 [B, wavelet_dim, seq_len] -> [B, seq_len, wavelet_dim]
        low_features = low_features.permute(0, 2, 1)
        components.append(low_features)
        
        # 将分量堆叠成序列 [num_components, B, seq_len, wavelet_dim]
        components_sequence = torch.stack(components, dim=0)
        
        # 调整维度为 [B, num_components, seq_len, wavelet_dim]
        components_sequence = components_sequence.permute(1, 0, 2, 3)
        
        return components_sequence

    def forward(self, x):
        """
        前向传播

        Args:
            x: 输入信号 [B, 2, L]，其中2表示I/Q通道

        Returns:
            output: 分类预测 [B, num_classes]
        """
        batch_size = x.size(0)

        # 使用初始卷积层处理输入
        x = self._process_initial_features(x)

        # 处理输入，获取低频和高频特征
        high_features_list, low_features = self._process_input(x)

        # 准备分量用于MRNN处理，得到形状为 [B, num_components, seq_len, wavelet_dim]
        components_sequence = self._prepare_wavelet_components_for_mrnn(high_features_list, low_features)

        # 调整维度以适应MMRNNClassifier
        # [B, num_components, seq_len, wavelet_dim] -> [B, num_components * seq_len, wavelet_dim]
        B, C, S, W = components_sequence.shape
        components_sequence = components_sequence.reshape(B, C * S, W)

        # 使用MMRNNClassifier，现在它将基于频率分量而非时间步处理序列
        output = self.mmrnn_classifier(components_sequence)

        return output

    def compute_lifting_loss(self, x):
        """
        计算小波提升方案的损失，用于正则化

        Args:
            x: 输入信号 [B, 2, L]

        Returns:
            loss_H: 高频分量损失 - 促进稀疏性
            loss_L: 低频分量损失 - 保持信号特征
        """
        # 先使用初始卷积层处理输入
        x = self._process_initial_features(x)

        # 小波分解
        high_list, low = self.wavelet(x)

        # 高频损失 - 最小化高频分量的绝对值和
        loss_H = sum(torch.mean(torch.abs(h)) for h in high_list)

        # 低频损失 - 保持低频分量与原始信号均值相似
        orig_mean = torch.mean(x, dim=2, keepdim=True)
        low_mean = torch.mean(low, dim=2, keepdim=True)
        loss_L = F.mse_loss(low_mean, orig_mean)

        return loss_H, loss_L
