#!/usr/bin/env python3
"""
MAMC训练脚本

这个脚本用于训练MAMC模型，支持多种数据集。
使用方法：python train.py
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import argparse
import numpy as np
from tqdm import tqdm
import logging
from datetime import datetime
import time
import json
from sklearn.metrics import f1_score, cohen_kappa_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from thop import profile, clever_format

# 导入模型和工具
from models import MAMCA, create_mamca_config, create_mamca_for_dataset
from utils.dataset import (
    load_rml_dataset, split_dataset, RML2016Dataset,
    get_hisar_data_loaders, get_torchsig_data_loaders, get_rml201801a_data_loaders
)

def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    log_file = os.path.join(output_dir, f'train_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def create_output_directories(config):
    """创建包含数据集和时间信息的输出目录结构"""
    dataset_type = config['data']['dataset_type']
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 基础输出目录
    base_output_dir = config['output_dir']

    # 创建细分的目录结构
    experiment_dir = os.path.join(base_output_dir, f"{dataset_type}_{timestamp}")

    # 创建子目录
    directories = {
        'experiment': experiment_dir,
        'models': os.path.join(experiment_dir, 'models'),
        'logs': os.path.join(experiment_dir, 'logs'),
        'results': os.path.join(experiment_dir, 'results'),
        'configs': os.path.join(experiment_dir, 'configs'),
        'plots': os.path.join(experiment_dir, 'plots')
    }

    # 创建所有目录
    for dir_path in directories.values():
        os.makedirs(dir_path, exist_ok=True)

    # 更新配置中的输出目录
    config['output_dir'] = experiment_dir
    config['directories'] = directories

    print(f"实验目录创建完成: {experiment_dir}")
    print(f"子目录包括: models, logs, results, configs, plots")

    return directories

def count_trainable_parameters(model):
    """计算模型的可训练参数数量"""
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    return trainable_params, total_params

def calculate_model_macs(model, input_shape, device):
    """计算模型的MACs (Multiply-Accumulate Operations)"""
    try:
        # 创建一个示例输入
        dummy_input = torch.randn(1, *input_shape).to(device)

        # 创建一个临时的模型副本来避免hook污染
        import copy
        temp_model = copy.deepcopy(model)
        temp_model.eval()

        # 计算MACs和参数数量
        macs, params = profile(temp_model, inputs=(dummy_input,), verbose=False)

        # 删除临时模型
        del temp_model

        # 格式化输出
        macs_formatted = clever_format([macs], "%.3f")
        params_formatted = clever_format([params], "%.3f")

        return macs, macs_formatted[0], params, params_formatted[0]
    except Exception as e:
        print(f"计算MACs时出错: {e}")
        print("跳过MACs计算，继续训练...")
        return 0, "N/A", 0, "N/A"

def measure_inference_time(model, data_loader, device, num_samples=100):
    """测量模型推理时间"""
    try:
        model.eval()
        inference_times = []

        with torch.no_grad():
            sample_count = 0
            for data, _, _ in data_loader:
                if sample_count >= num_samples:
                    break

                data = data.to(device)

                # 预热
                if sample_count == 0:
                    for _ in range(3):  # 减少预热次数
                        try:
                            _ = model(data)
                        except Exception as e:
                            print(f"预热时出错: {e}")
                            return 0.0

                # 测量推理时间
                torch.cuda.synchronize() if device.type == 'cuda' else None
                start_time = time.time()

                try:
                    _ = model(data)
                except Exception as e:
                    print(f"推理时出错: {e}")
                    return 0.0

                torch.cuda.synchronize() if device.type == 'cuda' else None
                end_time = time.time()

                batch_time = (end_time - start_time) / data.size(0)  # 每个样本的时间
                inference_times.extend([batch_time] * data.size(0))
                sample_count += data.size(0)

        if inference_times:
            avg_inference_time = np.mean(inference_times[:num_samples]) * 1000  # 转换为毫秒
            return avg_inference_time
        else:
            return 0.0
    except Exception as e:
        print(f"测量推理时间时出错: {e}")
        return 0.0

def generate_snr_confusion_matrices(model, test_loader, device, class_names, snr_ranges, save_dir):
    """生成不同SNR级别的混淆矩阵"""
    model.eval()

    # 收集所有预测结果
    all_predictions = []
    all_targets = []
    all_snrs = []

    with torch.no_grad():
        for data, target, snr in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            pred = output.argmax(dim=1)

            all_predictions.extend(pred.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
            all_snrs.extend(snr.cpu().numpy())

    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    all_snrs = np.array(all_snrs)

    # 定义SNR区间
    snr_bins = [
        (-20, -10, "Low SNR (-20~-10dB)"),
        (-10, 0, "Mid-Low SNR (-10~0dB)"),
        (0, 10, "Mid SNR (0~10dB)"),
        (10, 20, "Mid-High SNR (10~20dB)"),
        (20, 30, "High SNR (20~30dB)")
    ]

    confusion_matrices = {}

    for snr_min, snr_max, snr_label in snr_bins:
        # 筛选SNR范围内的样本
        snr_mask = (all_snrs >= snr_min) & (all_snrs < snr_max)

        if np.sum(snr_mask) == 0:
            continue

        snr_predictions = all_predictions[snr_mask]
        snr_targets = all_targets[snr_mask]

        # 计算混淆矩阵
        cm = confusion_matrix(snr_targets, snr_predictions)
        confusion_matrices[snr_label] = cm

        # 绘制混淆矩阵
        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.title(f'Confusion Matrix - {snr_label}')
        plt.xlabel('Predicted Class')
        plt.ylabel('True Class')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()

        # 保存图片
        safe_label = snr_label.replace('(', '').replace(')', '').replace('~', '_').replace(' ', '_')
        save_path = os.path.join(save_dir, f'confusion_matrix_{safe_label}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Confusion matrix saved: {save_path}")

    return confusion_matrices

def load_config(config_path='config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def get_data_loaders(config):
    """根据配置获取数据加载器"""
    dataset_type = config['data']['dataset_type']

    if dataset_type == 'rml':
        return get_rml_data_loaders(config)
    elif dataset_type == 'rml201801a':
        return get_rml201801a_data_loaders(config)
    elif dataset_type == 'hisar':
        return get_hisar_data_loaders(config)
    elif dataset_type.startswith('torchsig'):
        return get_torchsig_data_loaders(config)
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

def get_rml_data_loaders(config):
    """获取RML数据集的数据加载器"""
    # 获取RML配置
    rml_config = config['data']
    file_path = rml_config['rml_file_path']

    # 获取调制类型
    if rml_config['modulations'] is None:
        modulations = config['rml_class_names']
    else:
        modulations = rml_config['modulations']

    # 加载数据
    X, labels, snrs = load_rml_dataset(
        file_path=file_path,
        modulations=modulations,
        samples_per_key=rml_config.get('samples_per_key')
    )

    # 获取三个比例
    train_ratio = rml_config['train_ratio']
    test_ratio = config['data']['test_ratio']
    val_ratio = config['data']['val_ratio']

    # 验证比例总和是否为1
    total_ratio = train_ratio + test_ratio + val_ratio
    if abs(total_ratio - 1.0) > 1e-6:
        print(f"警告：数据集划分比例总和不为1.0，当前为{total_ratio}，将自动归一化")
        train_ratio = train_ratio / total_ratio
        test_ratio = test_ratio / total_ratio
        val_ratio = val_ratio / total_ratio

    # 首先分割出测试集
    temp_train_ratio = train_ratio + val_ratio  # 临时的训练+验证比例
    (X_temp, y_temp, snr_temp), (X_test, y_test, snr_test) = split_dataset(
        X, labels, snrs,
        train_ratio=temp_train_ratio,
        seed=config['training']['seed'],
        stratify_by_snr=rml_config['stratify_by_snr']
    )

    # 然后从剩余数据中分割训练集和验证集
    # 在剩余数据中，训练集应该占 train_ratio / (train_ratio + val_ratio)
    train_ratio_in_temp = train_ratio / temp_train_ratio

    def create_stratified_train_val_split(X, y, snr, train_ratio_in_temp, seed):
        """创建分层采样的训练和验证集划分"""
        np.random.seed(seed)

        # 获取唯一的SNR和标签值
        unique_snrs = np.unique(snr)
        unique_labels = np.unique(y)

        train_indices = []
        val_indices = []

        # 对每个SNR和标签组合进行分层采样
        for snr_val in unique_snrs:
            for label_val in unique_labels:
                # 找到符合条件的索引
                mask = (snr == snr_val) & (y == label_val)
                indices = np.where(mask)[0]

                if len(indices) == 0:
                    continue  # 跳过没有样本的组合

                # 随机打乱
                np.random.shuffle(indices)

                # 分割 - 注意这里是训练集在前
                n_train = int(len(indices) * train_ratio_in_temp)
                train_indices.extend(indices[:n_train])
                val_indices.extend(indices[n_train:])

        # 再次打乱
        np.random.shuffle(train_indices)
        np.random.shuffle(val_indices)

        return train_indices, val_indices

    # 创建分层采样的训练和验证索引
    train_indices, val_indices = create_stratified_train_val_split(
        X_temp, y_temp, snr_temp, train_ratio_in_temp, config['training']['seed']
    )

    # 根据索引分割数据
    X_train_final = X_temp[train_indices]
    y_train_final = y_temp[train_indices]
    snr_train_final = snr_temp[train_indices]

    X_val = X_temp[val_indices]
    y_val = y_temp[val_indices]
    snr_val = snr_temp[val_indices]

    print(f"RML数据集划分完成:")
    print(f"  训练集: {len(X_train_final)} 样本 ({len(X_train_final)/len(X)*100:.1f}%)")
    print(f"  验证集: {len(X_val)} 样本 ({len(X_val)/len(X)*100:.1f}%)")
    print(f"  测试集: {len(X_test)} 样本 ({len(X_test)/len(X)*100:.1f}%)")

    # 验证分层采样效果
    print(f"训练集SNR分布: {np.unique(snr_train_final, return_counts=True)[1][:5]}...")
    print(f"验证集SNR分布: {np.unique(snr_val, return_counts=True)[1][:5]}...")
    print(f"测试集SNR分布: {np.unique(snr_test, return_counts=True)[1][:5]}...")

    # 创建数据集
    train_dataset = RML2016Dataset(X_train_final, y_train_final, snr_train_final)
    val_dataset = RML2016Dataset(X_val, y_val, snr_val)
    test_dataset = RML2016Dataset(X_test, y_test, snr_test)

    # 创建数据加载器
    batch_size = config['training']['batch_size']
    num_workers = config['training']['num_workers']

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=num_workers)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)

    return train_loader, val_loader, test_loader

def create_model(config, actual_sequence_length=None):
    """创建模型 - 严格按照原始MAMC参数，所有数据集使用相同参数"""
    model_config = config['model']
    dataset_type = config['data']['dataset_type']

    # 根据数据集类型调整参数
    if dataset_type == 'rml':
        num_classes = len(config['rml_class_names'])
        sequence_length = actual_sequence_length or config['data']['sequence_lengths']['rml']
    elif dataset_type == 'rml201801a':
        num_classes = len(config['rml201801a_class_names'])
        sequence_length = actual_sequence_length or config['data']['sequence_lengths']['rml201801a']
    elif dataset_type == 'hisar':
        num_classes = len(config['hisar_class_names'])
        sequence_length = actual_sequence_length or config['data']['sequence_lengths']['hisar']
    elif dataset_type.startswith('torchsig'):
        num_classes = len(config['torchsig_class_names'])
        sequence_length = actual_sequence_length or config['data']['sequence_lengths'][dataset_type]
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

    # 使用原始MAMC的固定参数 - 不根据数据集修改
    d_model = model_config['d_model']                    # 16
    n_layer = model_config['n_layer']                    # 1
    d_state = model_config['d_state']                    # 16
    d_conv = model_config['d_conv']                      # 4
    expand = model_config['expand']                      # 2
    denoising_out_channels = model_config['denoising_out_channels']  # 16
    dropout_rate = model_config['dropout_rate']          # 0.15

    print(f"数据集: {dataset_type}")
    print(f"类别数: {num_classes}")
    print(f"序列长度: {sequence_length}")
    print(f"模型维度: {d_model}")
    print(f"层数: {n_layer}")
    print(f"状态维度: {d_state}")
    print(f"去噪输出通道: {denoising_out_channels}")
    print(f"Dropout率: {dropout_rate}")

    # 创建配置 - 严格按照原始MAMC
    mamca_config = create_mamca_config(
        d_model=d_model,
        n_layer=n_layer,
        d_state=d_state,
        d_conv=d_conv,
        expand=expand,
        rms_norm=model_config['rms_norm'],
        residual_in_fp32=model_config['residual_in_fp32'],
        fused_add_norm=model_config['fused_add_norm']
    )

    # 创建模型 - 严格按照原始MAMC
    model = MAMCA(
        config=mamca_config,
        length=sequence_length,
        num_claasses=num_classes  # 注意：原始MAMC使用的是num_claasses（有拼写错误）
    )

    return model

def train_epoch(model, train_loader, criterion, optimizer, device, config, logger):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0

    # 记录训练时间
    epoch_start_time = time.time()

    pbar = tqdm(train_loader, desc='Training')
    for batch_idx, (data, target, snr) in enumerate(pbar):
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()

        # 前向传播
        output = model(data)
        loss = criterion(output, target)

        # 反向传播
        loss.backward()

        # 梯度裁剪
        if config['training'].get('clip_grad', 0) > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['training']['clip_grad'])

        optimizer.step()

        # 统计
        total_loss += loss.item()
        pred = output.argmax(dim=1, keepdim=True)
        correct += pred.eq(target.view_as(pred)).sum().item()
        total += target.size(0)

        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })

    # 计算训练时间
    epoch_training_time = time.time() - epoch_start_time

    avg_loss = total_loss / len(train_loader)
    accuracy = 100. * correct / total

    logger.info(f'Train Loss: {avg_loss:.4f}, Train Acc: {accuracy:.2f}%, 训练时间: {epoch_training_time:.2f}s')

    return avg_loss, accuracy, epoch_training_time

def validate(model, val_loader, criterion, device, logger):
    """验证模型"""
    model.eval()
    total_loss = 0
    correct = 0
    total = 0
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for data, target, snr in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)

            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

            # 收集预测结果用于计算更多指标
            all_predictions.extend(pred.cpu().numpy().flatten())
            all_targets.extend(target.cpu().numpy())

    avg_loss = total_loss / len(val_loader)
    accuracy = 100. * correct / total

    # 计算额外的评估指标
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)

    macro_f1 = f1_score(all_targets, all_predictions, average='macro') * 100
    kappa = cohen_kappa_score(all_targets, all_predictions)

    logger.info(f'Val Loss: {avg_loss:.4f}, Val Acc: {accuracy:.2f}%, Macro-F1: {macro_f1:.2f}%, Kappa: {kappa:.4f}')

    return avg_loss, accuracy, macro_f1, kappa

def main():
    parser = argparse.ArgumentParser(description='MAMC训练脚本')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    args = parser.parse_args()

    # 如果没有提供任何命令行参数，直接使用默认配置
    if len(sys.argv) == 1:
        print("使用默认配置文件: config.yaml")
        args.config = 'config.yaml'

    # 加载配置
    config = load_config(args.config)

    # 创建输出目录结构
    directories = create_output_directories(config)

    # 设置日志（使用新的logs目录）
    logger = setup_logging(directories['logs'])
    logger.info(f"开始训练，配置文件: {args.config}")
    logger.info(f"数据集类型: {config['data']['dataset_type']}")
    logger.info(f"实验目录: {directories['experiment']}")

    # 保存配置文件副本到configs目录
    config_backup_path = os.path.join(directories['configs'], 'config_backup.yaml')
    with open(config_backup_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    logger.info(f"配置文件备份保存到: {config_backup_path}")

    # 设置设备
    device = torch.device(config['training']['device'] if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 设置随机种子
    torch.manual_seed(config['training']['seed'])
    np.random.seed(config['training']['seed'])
    
    # 获取数据加载器
    logger.info("加载数据...")
    train_loader, val_loader, test_loader = get_data_loaders(config)

    # 获取实际的序列长度
    actual_sequence_length = None
    if hasattr(train_loader.dataset, 'dataset'):
        # 如果是Subset，获取原始数据集
        original_dataset = train_loader.dataset.dataset
        if hasattr(original_dataset, 'actual_sequence_length'):
            actual_sequence_length = original_dataset.actual_sequence_length
    elif hasattr(train_loader.dataset, 'actual_sequence_length'):
        actual_sequence_length = train_loader.dataset.actual_sequence_length

    if actual_sequence_length:
        logger.info(f"检测到数据实际序列长度: {actual_sequence_length}")

    # 创建模型
    logger.info("创建模型...")
    model = create_model(config, actual_sequence_length)
    model = model.to(device)
    
    # 打印模型信息
    model_info = model.get_model_info()
    logger.info("模型信息:")
    for key, value in model_info.items():
        logger.info(f"  {key}: {value}")

    # 打印可训练参数数量
    trainable_params, total_params = count_trainable_parameters(model)
    logger.info(f"可训练参数数量: {trainable_params:,}")
    logger.info(f"总参数数量: {total_params:,}")
    logger.info(f"可训练参数比例: {trainable_params/total_params*100:.2f}%")

    # 计算模型MACs
    logger.info("计算模型MACs...")
    # 使用实际的序列长度
    seq_length = actual_sequence_length or config['data']['sequence_lengths'][config['data']['dataset_type']]
    input_shape = (2, seq_length)
    macs, macs_formatted, params_thop, params_formatted = calculate_model_macs(model, input_shape, device)
    if macs_formatted != "N/A":
        logger.info(f"模型MACs: {macs_formatted}")
        logger.info(f"THOP参数数量: {params_formatted}")
    else:
        logger.info("MACs计算失败，跳过")

    # 测量推理时间
    logger.info("测量推理时间...")
    avg_inference_time = measure_inference_time(model, val_loader, device, num_samples=100)
    if avg_inference_time > 0:
        logger.info(f"平均推理时间: {avg_inference_time:.4f} ms/sample")
    else:
        logger.info("推理时间测量失败，跳过")

    # 预先定义class_names，避免早停时未定义的问题
    dataset_type = config['data']['dataset_type']
    if dataset_type == 'rml':
        class_names = config['rml_class_names']
    elif dataset_type == 'rml201801a':
        class_names = config['rml201801a_class_names']
    elif dataset_type == 'hisar':
        class_names = config['hisar_class_names']
    elif dataset_type.startswith('torchsig'):
        class_names = config['torchsig_class_names']
    else:
        class_names = [f'Class_{i}' for i in range(config['model']['num_classes'])]

    # 创建损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(
        model.parameters(),
        lr=config['training']['learning_rate'],
        weight_decay=config['training']['weight_decay']
    )
    
    # 学习率调度器
    if config['training']['scheduler'] == 'cosine':
        # 确保min_lr是浮点数
        min_lr = float(config['training']['min_lr'])
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=config['training']['epochs'],
            eta_min=min_lr
        )
    else:
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='max',
            patience=config['training']['patience'],
            factor=config['training']['factor']
        )
    
    # 训练循环
    best_acc = 0
    best_f1 = 0
    best_kappa = 0
    patience_counter = 0
    training_history = {
        'epochs': [],
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': [],
        'val_f1': [],
        'val_kappa': [],
        'epoch_time': [],
        'learning_rate': []
    }

    for epoch in range(config['training']['epochs']):
        epoch_start_time = time.time()
        logger.info(f"Epoch {epoch+1}/{config['training']['epochs']}")

        # 训练
        train_loss, train_acc, epoch_training_time = train_epoch(
            model, train_loader, criterion, optimizer, device, config, logger
        )

        # 验证
        val_loss, val_acc, val_f1, val_kappa = validate(
            model, val_loader, criterion, device, logger
        )

        # 记录训练时间
        epoch_time = time.time() - epoch_start_time
        logger.info(f"Epoch {epoch+1} 训练时间: {epoch_time:.2f} 秒")

        # 记录训练历史
        training_history['epochs'].append(epoch + 1)
        training_history['train_loss'].append(train_loss)
        training_history['train_acc'].append(train_acc)
        training_history['val_loss'].append(val_loss)
        training_history['val_acc'].append(val_acc)
        training_history['val_f1'].append(val_f1)
        training_history['val_kappa'].append(val_kappa)
        training_history['epoch_time'].append(epoch_time)
        training_history['learning_rate'].append(optimizer.param_groups[0]['lr'])

        # 学习率调度
        if config['training']['scheduler'] == 'cosine':
            scheduler.step()
        else:
            scheduler.step(val_acc)

        # 保存最佳模型
        if val_acc > best_acc:
            best_acc = val_acc
            best_f1 = val_f1
            best_kappa = val_kappa
            patience_counter = 0

            # 保存最佳模型到models目录
            best_model_path = os.path.join(directories['models'], 'best_model.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
                'best_f1': best_f1,
                'best_kappa': best_kappa,
                'config': config,
                'model_info': model.get_model_info(),
                'training_history': training_history
            }, best_model_path)

            # 同时保存一个带时间戳的模型副本
            timestamp_model_path = os.path.join(directories['models'], f'model_epoch_{epoch+1}_acc_{val_acc:.2f}.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
                'best_f1': best_f1,
                'best_kappa': best_kappa,
                'config': config,
                'model_info': model.get_model_info(),
                'training_history': training_history
            }, timestamp_model_path)

            logger.info(f"保存最佳模型，验证准确率: {best_acc:.2f}%, Macro-F1: {best_f1:.2f}%, Kappa: {best_kappa:.4f}")
            logger.info(f"模型保存到: {best_model_path}")
            logger.info(f"模型副本保存到: {timestamp_model_path}")
        else:
            patience_counter += 1

        # 早停
        early_stopping = config['training'].get('early_stopping', False)
        early_stop_patience = config['training'].get('early_stop_patience', 20)
        if early_stopping and patience_counter >= early_stop_patience:
            logger.info(f"早停触发，最佳验证准确率: {best_acc:.2f}%")
            break

    # 保存训练历史到results目录
    history_file = os.path.join(directories['results'], 'training_history.json')
    with open(history_file, 'w') as f:
        json.dump(training_history, f, indent=2)
    logger.info(f"训练历史保存到: {history_file}")

    # 生成SNR混淆矩阵
    logger.info("生成SNR混淆矩阵...")
    snr_ranges = config['data']['snr_ranges'][dataset_type]
    confusion_matrices = generate_snr_confusion_matrices(
        model, test_loader, device, class_names, snr_ranges, directories['plots']
    )

    # 保存训练摘要信息
    total_training_time = sum(training_history['epoch_time'])
    avg_epoch_time = total_training_time / len(training_history['epoch_time'])

    training_summary = {
        'dataset_type': config['data']['dataset_type'],
        'model_type': 'MAMC',
        'experiment_timestamp': directories['experiment'].split('_')[-1],
        'total_epochs': len(training_history['epochs']),
        'best_validation_accuracy': float(best_acc),
        'best_macro_f1': float(best_f1),
        'best_kappa': float(best_kappa),
        'total_training_time_seconds': float(total_training_time),
        'total_training_time_minutes': float(total_training_time / 60),
        'average_epoch_time_seconds': float(avg_epoch_time),
        'final_learning_rate': float(optimizer.param_groups[0]['lr']),
        'trainable_parameters': trainable_params,
        'total_parameters': total_params,
        'model_macs': macs if macs_formatted != "N/A" else "N/A",
        'model_macs_formatted': macs_formatted,
        'average_inference_time_ms': avg_inference_time if avg_inference_time > 0 else "N/A",
        'config_file': args.config
    }

    summary_file = os.path.join(directories['results'], 'training_summary.json')
    with open(summary_file, 'w') as f:
        json.dump(training_summary, f, indent=2)
    logger.info(f"训练摘要保存到: {summary_file}")

    logger.info("训练完成！")
    logger.info(f"最佳验证准确率: {best_acc:.2f}%")
    logger.info(f"最佳Macro-F1: {best_f1:.2f}%")
    logger.info(f"最佳Kappa: {best_kappa:.4f}")
    logger.info(f"总训练时间: {total_training_time:.2f} 秒 ({total_training_time/60:.1f} 分钟)")
    logger.info(f"平均每轮训练时间: {avg_epoch_time:.2f} 秒")
    if macs_formatted != "N/A":
        logger.info(f"模型MACs: {macs_formatted}")
    if avg_inference_time > 0:
        logger.info(f"平均推理时间: {avg_inference_time:.4f} ms/sample")
    logger.info(f"实验结果保存在: {directories['experiment']}")
    logger.info(f"SNR混淆矩阵保存在: {directories['plots']}")

if __name__ == '__main__':
    main()
