# MAMC (Mamba-based Automatic Modulation Classification) 配置文件
# 支持多种数据集的MAMC模型训练和测试

# 数据集配置
data:
  dataset_type: 'rml'  # 可选: 'rml', 'rml201801a', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096'

  # 统一的数据集划分比例
  train_ratio: 0.7     # 训练集比例 (70%)
  test_ratio: 0.15     # 测试集比例 (15%)
  val_ratio: 0.15      # 验证集比例 (15%)
  normalize: true      # 是否对信号进行归一化

  # SNR范围配置 - 按照WNN-MRNN格式
  snr_ranges:
    rml: [-20, 18]       # RML数据集SNR范围
    rml201801a: [-20, 30]  # RML2018.01a数据集SNR范围
    hisar: [-20, 18]     # Hisar数据集SNR范围
    torchsig1024: [0, 30]    # TorchSig数据集SNR范围
    torchsig2048: [0, 30]
    torchsig4096: [0, 30]

  # 信号序列长度配置 - MAMC模型会自适应这些长度
  sequence_lengths:
    rml: 128            # RML数据集序列长度
    rml201801a: 1024    # RML2018.01a数据集序列长度
    hisar: 1024         # Hisar数据集序列长度
    torchsig1024: 1024  # torchsig1024数据集序列长度
    torchsig2048: 2048  # torchsig2048数据集序列长度
    torchsig4096: 4096  # torchsig4096数据集序列长度
  
  # RML数据集配置
  rml_file_path: 'data/RML2016.10a_dict.pkl'
  modulations: null    # null表示使用所有调制类型
  snr_range: [-20, 18]  # SNR范围 [min_snr, max_snr]
  stratify_by_snr: true  # 是否按SNR分层抽样
  samples_per_key: null  # 每个(调制类型,SNR)对的样本数量，null表示使用所有样本

  # RML2018.01a数据集配置
  rml201801a_file_path: 'data/GOLD_XYZ_OSC.0001_1024.hdf5'
  rml201801a_modulations: null  # null表示使用所有24种调制类型，或指定列表如: ['BPSK', 'QPSK', '8PSK', '16QAM']
  rml201801a_use_all_snr: true  # 是否使用所有SNR级别的数据，false表示只使用最后4个SNR级别

  # HisarMod数据集配置
  train_path: 'data/hisar/train_data.mat'
  train_labels_path: 'data/hisar/train_labels.csv'
  train_snr_path: 'data/hisar/train_snr.csv'
  
  test_path: 'data/hisar/test_data.mat'
  test_labels_path: 'data/hisar/test_labels.csv'
  test_snr_path: 'data/hisar/test_snr.csv'
  
  # TorchSig数据集配置 - 1024样本长度
  torchsig1024_train_path: 'data/torchsig1024/train_data.mat'
  torchsig1024_train_labels_path: 'data/torchsig1024/train_labels.csv'
  torchsig1024_train_snr_path: 'data/torchsig1024/train_snr.csv'
  
  torchsig1024_test_path: 'data/torchsig1024/test_data.mat'
  torchsig1024_test_labels_path: 'data/torchsig1024/test_labels.csv'
  torchsig1024_test_snr_path: 'data/torchsig1024/test_snr.csv'

  # TorchSig数据集配置 - 2048样本长度
  torchsig2048_train_path: 'data/torchsig2048/train_data.mat'
  torchsig2048_train_labels_path: 'data/torchsig2048/train_labels.csv'
  torchsig2048_train_snr_path: 'data/torchsig2048/train_snr.csv'
  
  torchsig2048_test_path: 'data/torchsig2048/test_data.mat'
  torchsig2048_test_labels_path: 'data/torchsig2048/test_labels.csv'
  torchsig2048_test_snr_path: 'data/torchsig2048/test_snr.csv'

  # TorchSig数据集配置 - 4096样本长度
  torchsig4096_train_path: 'data/torchsig4096/train_data.mat'
  torchsig4096_train_labels_path: 'data/torchsig4096/train_labels.csv'
  torchsig4096_train_snr_path: 'data/torchsig4096/train_snr.csv'
  
  torchsig4096_test_path: 'data/torchsig4096/test_data.mat'
  torchsig4096_test_labels_path: 'data/torchsig4096/test_labels.csv'
  torchsig4096_test_snr_path: 'data/torchsig4096/test_snr.csv'

# 模型配置 - 严格按照原始MAMC参数，所有数据集使用相同参数
model:
  type: 'MAMC'
  in_channels: 2
  num_classes: 11              # 会根据数据集自动调整
  sequence_length: 128         # 会根据数据集自动调整

  # 原始MAMC模型参数 - 不根据数据集修改
  d_model: 16                  # 原始MAMC的d_model
  n_layer: 1                   # 原始MAMC的n_layer
  d_state: 16                  # 原始MAMC的d_state
  d_conv: 4                    # 原始MAMC的d_conv
  expand: 2                    # 原始MAMC的expand
  denoising_out_channels: 16   # 原始MAMC的denoising输出通道
  denoising_num_blocks: 2      # 原始MAMC的denoising块数
  dropout_rate: 0.15           # 原始MAMC的dropout率

  # Mamba特定配置 - 按照原始MAMC
  rms_norm: true
  residual_in_fp32: false
  fused_add_norm: false

# 训练参数
training:
  epochs: 100
  batch_size: 128  
  learning_rate: 0.01
  weight_decay: 0.0001

  # 学习率调度器
  scheduler: 'cosine'  # 可选: 'cosine', 'plateau'
  min_lr: 1e-7
  patience: 10
  factor: 0.5

  # 早停配置
  early_stopping: true
  early_stop_patience: 20

  # 其他训练参数
  clip_grad: 1.0
  seed: 42
  device: 'cuda'
  # 大内存环境数据加载优化
  num_workers: 12         # 大内存环境使用更多线程
  pin_memory: true        # 使用锁页内存
  prefetch_factor: 8      # 大幅增加预取因子
  persistent_workers: true # 使用持久化工作进程

# 输出配置
output_dir: 'saved_models/mamc'

# 数据集类别名称 - 按照WNN-MRNN格式
rml_class_names: ['8PSK', 'AM-DSB', 'AM-SSB', 'BPSK', 'CPFSK', 'GFSK', 'PAM4', 'QAM16', 'QAM64', 'QPSK', 'WBFM']

# RML2018.01a数据集的24种调制类型
rml201801a_class_names:
  - "OOK"
  - "4ASK"
  - "8ASK"
  - "BPSK"
  - "QPSK"
  - "8PSK"
  - "16PSK"
  - "32PSK"
  - "16APSK"
  - "32APSK"
  - "64APSK"
  - "128APSK"
  - "16QAM"
  - "32QAM"
  - "64QAM"
  - "128QAM"
  - "256QAM"
  - "AM-SSB-WC"
  - "AM-SSB-SC"
  - "AM-DSB-WC"
  - "AM-DSB-SC"
  - "FM"
  - "GMSK"
  - "OQPSK"

hisar_class_names: ['BPSK', 'QPSK', '8PSK', '16PSK', '32PSK', '64PSK', '4QAM', '8QAM', '16QAM', '32QAM', '64QAM',
                     '128QAM', '256QAM', '2FSK', '4FSK', '8FSK', '16FSK', '4PAM', '8PAM', '16PAM', 'AM-DSB',
                     'AM-DSB-SC', 'AM-USB', 'AM-LSB', 'FM', 'PM']

torchsig_class_names: ['ook', 'bpsk', 'qpsk', '8psk', '16psk', '32psk', '16apsk', '32apsk', '64apsk', '128apsk',
                       '16qam', '32qam', '64qam', '128qam', '256qam', 'am-ssb-wc', 'am-ssb-sc', 'am-dsb-wc',
                       'am-dsb-sc', 'fm', 'gmsk', 'oqpsk', 'msk', 'gfsk', 'cpfsk']
