# WNN-MRNN 超参数优化器使用说明

## 概述

`hyperparameter_optimizer.py` 是一个独立的超参数优化工具，专门用于优化 WNN-MRNN 模型的四个关键参数：
- `wavelet_dim`: 小波维度
- `rnn_dim`: RNN维度
- `num_levels`: 小波分解层数
- `num_layers`: MMRNNCell层数

## 特点

- **独立运行**: 不影响原有的训练流程和文件结构
- **多数据集支持**: 可以同时在多个数据集上进行优化
- **智能剪枝**: 使用 Optuna 的剪枝机制，自动淘汰表现差的参数组合
- **实时保存**: 每次试验后立即保存结果，支持断点续传
- **多重备份**: 提供多种格式的结果文件，确保数据安全
- **自定义配置**: 所有参数都可在文件顶部统一配置

## 安装依赖

在使用优化器之前，需要安装 Optuna：

```bash
pip install optuna
```

## 使用方法

### 1. 选择优化模式和预设

在 `hyperparameter_optimizer.py` 文件顶部配置：

```python
# 🚀 快速配置预设（选择一个取消注释）
# QUICK_PRESET = 'fast'      # 快速测试: 8轮训练, 2轮耐心, 20次试验
# QUICK_PRESET = 'balanced'  # 平衡模式: 12轮训练, 3轮耐心, 50次试验
# QUICK_PRESET = 'thorough'  # 深度优化: 20轮训练, 5轮耐心, 100次试验
QUICK_PRESET = 'fast'  # 当前使用的预设

# 基本配置
OPTIMIZATION_CONFIG = {
    'optimization_mode': 'separate',  # 'separate': 分别优化, 'combined': 联合优化
    'datasets': ['rml201801a', 'hisar', 'torchsig1024'],
    'n_trials_per_dataset': 50,  # 每个数据集的试验次数
    # ... 其他配置会被预设覆盖
}
```

#### 优化模式说明：

- **🎯 separate (分别优化)**: **推荐模式**
  - 为每个数据集单独寻找最优参数
  - 每个数据集进行N次试验
  - 总试验次数 = 数据集数量 × 每数据集试验次数
  - 适合：不同数据集特性差异较大的情况

- **🔗 combined (联合优化)**:
  - 寻找在所有数据集上平均表现最好的参数
  - 每次试验在所有数据集上测试
  - 适合：需要通用参数的情况

#### 预设模式说明：

- **🚀 fast**: 快速测试模式 (8轮训练, 20次试验)
- **⚖️ balanced**: 平衡模式 (12轮训练, 50次试验) - 推荐
- **🎯 thorough**: 深度优化模式 (20轮训练, 100次试验)

### 2. 运行优化

```bash
cd WNN-MRNN
python hyperparameter_optimizer.py
```

运行时会显示当前配置和预估时间：
```
🎯 WNN-MRNN 超参数优化配置
======================================================================
🚀 当前预设: FAST - 快速测试模式 - 适合初步筛选参数
----------------------------------------------------------------------
优化模式: 分别优化
目标数据集: ['rml201801a', 'hisar', 'torchsig1024']
每个数据集试验次数: 20
总试验次数: 3 × 20 = 60
训练轮数: 8 (最少 3 轮)
预估总时间: 24 分钟 (0.4 小时)
```

### 3. 参数说明

- `datasets_to_optimize`: 要优化的数据集列表，可选：
  - `'rml'`: RML2016.10a 数据集
  - `'rml201801a'`: RML2018.01a 数据集
  - `'hisar'`: HisarMod 数据集
  - `'torchsig1024'`: TorchSig 1024 数据集
  - `'torchsig2048'`: TorchSig 2048 数据集
  - `'torchsig4096'`: TorchSig 4096 数据集

- `n_trials`: 优化试验次数，建议值：
  - 快速测试: 10-20 次
  - 正常优化: 30-50 次
  - 深度优化: 100+ 次

## 优化参数范围

优化器会根据不同数据集自动调整参数搜索范围：

### RML 数据集 (rml, rml201801a)
- `wavelet_dim`: [32, 64, 128]
- `rnn_dim`: [32, 64, 128]
- `num_levels`: [2, 3, 4, 5]
- `num_layers`: [1, 2, 3, 4]

### Hisar 数据集
- `wavelet_dim`: [64, 128, 256]
- `rnn_dim`: [64, 128, 256]
- `num_levels`: [2, 3, 4, 5]
- `num_layers`: [1, 2, 3, 4]

### TorchSig 数据集
- `wavelet_dim`: [128, 256, 512]
- `rnn_dim`: [128, 256, 512]
- `num_levels`: [2, 3, 4, 5]
- `num_layers`: [1, 2, 3, 4]

## 输出结果

### 1. 控制台输出
优化过程中会实时显示：
- 当前试验编号和参数
- 每个数据集的验证准确率
- 平均准确率
- 🎉 发现新最佳结果时的提示

### 2. 实时保存文件

#### 分别优化模式 (`optimization_results_[study_name]_separate/`)：
- **`[dataset]_best_results.json`**: 🔥 **各数据集最佳结果**（实时更新）
- **`[dataset]_ranked_results.json`**: 🏆 **各数据集实时排名**（实时更新）
- **`[dataset]_all_results.json`**: 📊 **各数据集试验历史**（实时更新）
- **`optimization_summary.yaml`**: 📋 **汇总结果**

#### 联合优化模式 (`optimization_results_[study_name]/`)：
- **`current_best_results.json`**: 🔥 **当前最佳结果**（实时更新）
- **`ranked_results.json`**: 🏆 **实时参数排名**（实时更新）
- **`all_trial_results.json`**: 📊 **所有试验历史**（实时更新）

### 3. 最终结果文件
- **`final_optimization_results.yaml`**: 最终结果（YAML格式）
- **`optimization_summary.json`**: 汇总结果（JSON格式，更详细）
- **`[study_name]_optimization.log`**: 详细的优化日志

### 4. 数据库存储（可选）
- **`wnn_mrnn_optimization.db`**: SQLite数据库，支持断点续传

## 结果应用

优化完成后，将最佳参数应用到配置文件：

1. 打开 `config.yaml`
2. 更新对应数据集的参数：

```yaml
model:
  dataset_specific_params:
    your_dataset:
      wavelet_dim: 128  # 使用优化得到的值
      rnn_dim: 256      # 使用优化得到的值
  
  num_levels: 3         # 使用优化得到的值
  num_layers: 2         # 使用优化得到的值
```

## 断点续传功能

### 如何使用断点续传

1. **自动恢复**: 重新运行优化器时，会自动加载之前的结果
2. **查看当前最佳**: 随时查看 `current_best_results.json` 获取当前最优参数
3. **数据库支持**: 启用数据库存储后，支持完整的断点续传

### 中途停止后的操作

```bash
# 快速查看排名
## 分别优化模式：
python quick_ranking.py                    # 查看所有数据集汇总
python quick_ranking.py rml201801a        # 查看指定数据集排名
python quick_ranking.py hisar 20          # 查看指定数据集TOP 20

## 联合优化模式：
python quick_ranking.py                    # 查看整体排名
python quick_ranking.py 20                # 查看TOP 20

# 查看详细结果
python view_optimization_results.py

# 继续优化（会自动从上次停止的地方继续）
python hyperparameter_optimizer.py
```

## 注意事项

1. **训练时间**: 优化器会自动将训练轮数减少以加快优化速度（可在配置中调整）
2. **早停机制**: 启用早停以避免过度训练
3. **内存使用**: 多数据集优化可能需要较大内存
4. **GPU 使用**: 建议在 GPU 环境下运行以加快训练速度
5. **实时保存**: 每次试验后立即保存，确保不丢失结果

## 故障排除

### 常见问题

1. **内存不足**: 减少 `batch_size` 或减少同时优化的数据集数量
2. **训练失败**: 检查数据路径是否正确
3. **优化时间过长**: 减少 `n_trials` 或启用更激进的剪枝策略

### 调试模式

如需调试，可以在代码中添加更多日志输出或减少试验次数进行测试。
