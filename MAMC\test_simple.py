#!/usr/bin/env python3
"""
MAMC简化测试脚本 - 只生成混淆矩阵图片和保存数据

这个脚本用于测试训练好的MAMC模型，只保留两个核心功能：
1. 生成不同SNR下的混淆矩阵图片
2. 保存相关的数据（混淆矩阵数据、SNR结果等）

使用方法：python test_simple.py --config config.yaml --model_path saved_models/best_model.pth
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import argparse
import numpy as np
from tqdm import tqdm
import logging
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import json
from sklearn.metrics import confusion_matrix, f1_score, cohen_kappa_score

# 导入模型和工具
from models import MAMCA, create_mamca_config
from utils.dataset import (
    load_rml_dataset, split_dataset, RML2016Dataset,
    get_hisar_data_loaders, get_torchsig_data_loaders, get_rml201801a_data_loaders
)

def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    log_file = os.path.join(output_dir, f'test_simple_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def get_data_loaders(config):
    """根据配置获取数据加载器"""
    dataset_type = config['data']['dataset_type']

    if dataset_type == 'rml':
        return get_rml_data_loaders(config)
    elif dataset_type == 'rml201801a':
        return get_rml201801a_data_loaders(config)
    elif dataset_type == 'hisar':
        return get_hisar_data_loaders(config)
    elif dataset_type.startswith('torchsig'):
        return get_torchsig_data_loaders(config)
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

def get_rml_data_loaders(config):
    """获取RML数据集的数据加载器"""
    # 获取RML配置
    rml_config = config['data']
    file_path = rml_config['rml_file_path']
    
    # 获取调制类型
    if rml_config['modulations'] is None:
        modulations = config['rml_class_names']
    else:
        modulations = rml_config['modulations']
    
    # 加载数据
    X, labels, snrs = load_rml_dataset(
        file_path=file_path,
        modulations=modulations,
        samples_per_key=rml_config.get('samples_per_key')
    )
    
    # 获取三个比例
    train_ratio = rml_config['train_ratio']
    test_ratio = config['data']['test_ratio']
    val_ratio = config['data']['val_ratio']

    # 验证比例总和是否为1
    total_ratio = train_ratio + test_ratio + val_ratio
    if abs(total_ratio - 1.0) > 1e-6:
        print(f"警告：数据集划分比例总和不为1.0，当前为{total_ratio}，将自动归一化")
        train_ratio = train_ratio / total_ratio
        test_ratio = test_ratio / total_ratio
        val_ratio = val_ratio / total_ratio

    # 首先分割出测试集
    temp_train_ratio = train_ratio + val_ratio  # 临时的训练+验证比例
    (X_temp, y_temp, snr_temp), (X_test, y_test, snr_test) = split_dataset(
        X, labels, snrs,
        train_ratio=temp_train_ratio,
        seed=config['training']['seed'],
        stratify_by_snr=rml_config['stratify_by_snr']
    )

    # 创建测试数据集
    test_dataset = RML2016Dataset(X_test, y_test, snr_test)
    
    # 创建数据加载器
    batch_size = config['training']['batch_size']
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return None, None, test_loader

def load_model(model_path, config, device):
    """加载训练好的模型"""
    checkpoint = torch.load(model_path, map_location=device)

    # 从检查点获取配置（如果有的话）
    if 'config' in checkpoint:
        saved_config = checkpoint['config']
        # 使用保存的模型配置
        model_config = saved_config['model']
        dataset_type = saved_config['data']['dataset_type']
    else:
        # 使用当前配置
        model_config = config['model']
        dataset_type = config['data']['dataset_type']

    # 根据数据集类型调整参数
    if dataset_type == 'rml':
        num_classes = len(config['rml_class_names'])
        sequence_length = config['data']['sequence_lengths']['rml']
    elif dataset_type == 'rml201801a':
        num_classes = len(config['rml201801a_class_names'])
        sequence_length = config['data']['sequence_lengths']['rml201801a']
    elif dataset_type == 'hisar':
        num_classes = len(config['hisar_class_names'])
        sequence_length = config['data']['sequence_lengths']['hisar']
    elif dataset_type.startswith('torchsig'):
        num_classes = len(config['torchsig_class_names'])
        sequence_length = config['data']['sequence_lengths'][dataset_type]
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

    # 使用原始MAMC的固定参数 - 不根据数据集修改
    d_model = model_config.get('d_model', 16)
    n_layer = model_config.get('n_layer', 1)
    d_state = model_config.get('d_state', 16)
    d_conv = model_config.get('d_conv', 4)
    expand = model_config.get('expand', 2)
    denoising_out_channels = model_config.get('denoising_out_channels', 16)
    dropout_rate = model_config.get('dropout_rate', 0.15)

    print(f"加载模型 - 数据集: {dataset_type}, 序列长度: {sequence_length}")

    # 创建配置
    mamca_config = create_mamca_config(
        d_model=d_model,
        n_layer=n_layer,
        d_state=d_state,
        d_conv=d_conv,
        expand=expand,
        rms_norm=model_config.get('rms_norm', True),
        residual_in_fp32=model_config.get('residual_in_fp32', False),
        fused_add_norm=model_config.get('fused_add_norm', False)
    )

    # 创建模型 - 严格按照原始MAMC
    model = MAMCA(
        config=mamca_config,
        length=sequence_length,
        num_claasses=num_classes  # 注意：原始MAMC使用的是num_claasses（有拼写错误）
    )

    # 加载权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)

    return model

def test_model(model, test_loader, device, logger):
    """测试模型并收集预测结果"""
    model.eval()
    correct = 0
    total = 0
    all_predictions = []
    all_targets = []
    all_snrs = []

    with torch.no_grad():
        pbar = tqdm(test_loader, desc='Testing')
        for data, target, snr in pbar:
            data, target = data.to(device), target.to(device)

            output = model(data)
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

            # 收集预测结果
            all_predictions.extend(pred.cpu().numpy().flatten())
            all_targets.extend(target.cpu().numpy())
            all_snrs.extend(snr.numpy())

            # 更新进度条
            pbar.set_postfix({'Acc': f'{100.*correct/total:.2f}%'})

    accuracy = 100. * correct / total

    # 转换为numpy数组
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    all_snrs = np.array(all_snrs)

    # 计算额外的评估指标
    macro_f1 = f1_score(all_targets, all_predictions, average='macro') * 100
    kappa = cohen_kappa_score(all_targets, all_predictions)

    logger.info(f'Test Accuracy: {accuracy:.2f}%')
    logger.info(f'Macro-F1: {macro_f1:.2f}%')
    logger.info(f'Kappa: {kappa:.4f}')

    return accuracy, macro_f1, kappa, all_predictions, all_targets, all_snrs

def generate_confusion_matrices_and_save_data(predictions, targets, snrs, config, output_dir, logger):
    """生成混淆矩阵图片并保存完善的数据文件"""
    dataset_type = config['data']['dataset_type']

    # 获取类别名称
    if dataset_type == 'rml':
        class_names = config['rml_class_names']
    elif dataset_type == 'rml201801a':
        class_names = config['rml201801a_class_names']
    elif dataset_type == 'hisar':
        class_names = config['hisar_class_names']
    elif dataset_type.startswith('torchsig'):
        class_names = config['torchsig_class_names']
    else:
        class_names = [f'Class_{i}' for i in range(len(np.unique(targets)))]

    # 创建输出目录 - 只创建必要的目录
    snr_cm_dir = os.path.join(output_dir, 'plots', 'snr_confusion_matrices')
    os.makedirs(snr_cm_dir, exist_ok=True)

    # 按SNR生成混淆矩阵
    logger.info("生成各SNR下的混淆矩阵...")
    unique_snrs = np.unique(snrs)

    # 完善的混淆矩阵数据结构，包含调制方式信息
    snr_confusion_matrices = {
        'metadata': {
            'class_names': class_names,
            'dataset_type': dataset_type,
            'total_samples': len(targets),
            'snr_list': unique_snrs.tolist(),
            'num_classes': len(class_names)
        },
        'confusion_matrices': {}
    }

    for snr in unique_snrs:
        mask = snrs == snr
        snr_targets = targets[mask]
        snr_predictions = predictions[mask]

        if len(snr_targets) > 0:
            # 计算该SNR下的指标
            snr_acc = np.mean(snr_targets == snr_predictions) * 100
            snr_f1 = f1_score(snr_targets, snr_predictions, average='macro') * 100
            snr_kappa = cohen_kappa_score(snr_targets, snr_predictions)

            # 生成该SNR下的混淆矩阵
            snr_cm = confusion_matrix(snr_targets, snr_predictions)

            # 保存完善的混淆矩阵数据，包含所有必要信息
            snr_confusion_matrices['confusion_matrices'][f'SNR_{int(snr)}dB'] = {
                'snr': float(snr),
                'confusion_matrix': snr_cm.tolist(),
                'class_names': class_names,
                'metrics': {
                    'accuracy': snr_acc,
                    'macro_f1': snr_f1,
                    'kappa': snr_kappa
                },
                'sample_count': len(snr_targets)
            }

            logger.info(f'SNR {snr:2.0f} dB: Acc={snr_acc:.2f}%, F1={snr_f1:.2f}%, Kappa={snr_kappa:.4f} (n={len(snr_targets)})')

            # 生成并保存该SNR下的混淆矩阵图片
            plt.figure(figsize=(10, 8))
            sns.heatmap(snr_cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=class_names, yticklabels=class_names)
            plt.title(f'Confusion Matrix - SNR {int(snr)} dB\nAcc: {snr_acc:.1f}%, F1: {snr_f1:.1f}%, Kappa: {snr_kappa:.3f}')
            plt.xlabel('Predicted')
            plt.ylabel('Actual')
            plt.xticks(rotation=45)
            plt.yticks(rotation=0)
            plt.tight_layout()
            plt.savefig(os.path.join(snr_cm_dir, f'confusion_matrix_snr_{int(snr)}dB.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

    # 保存完善的SNR混淆矩阵数据文件
    with open(os.path.join(output_dir, 'snr_confusion_matrices.json'), 'w') as f:
        json.dump(snr_confusion_matrices, f, indent=2)

    logger.info(f"生成完成！")
    logger.info(f"混淆矩阵图片保存在: {snr_cm_dir}/")
    logger.info(f"混淆矩阵数据保存在: {os.path.join(output_dir, 'snr_confusion_matrices.json')}")

    return snr_confusion_matrices

def find_latest_experiment_dir(base_dir, dataset_type):
    """查找最新的实验目录"""
    if not os.path.exists(base_dir):
        return None

    # 查找匹配的实验目录
    experiment_dirs = []
    for item in os.listdir(base_dir):
        if item.startswith(f"{dataset_type}_") and os.path.isdir(os.path.join(base_dir, item)):
            experiment_dirs.append(item)

    if not experiment_dirs:
        return None

    # 按时间戳排序，返回最新的
    experiment_dirs.sort(reverse=True)
    return os.path.join(base_dir, experiment_dirs[0])

def create_test_output_directories(config, model_path):
    """创建简化的测试输出目录结构"""
    dataset_type = config['data']['dataset_type']
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 基础输出目录
    base_output_dir = config['output_dir']

    # 创建测试结果目录
    test_dir = os.path.join(base_output_dir, f"test_simple_{dataset_type}_{timestamp}")

    # 只创建必要的目录
    os.makedirs(test_dir, exist_ok=True)
    os.makedirs(os.path.join(test_dir, 'logs'), exist_ok=True)

    print(f"测试结果目录: {test_dir}")

    return test_dir

def main():
    parser = argparse.ArgumentParser(description='MAMC简化测试脚本 - 只生成混淆矩阵和保存数据')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--model_path', type=str, default=None, help='模型文件路径')
    parser.add_argument('--output_dir', type=str, default=None, help='结果输出目录')
    parser.add_argument('--auto_find', action='store_true', help='自动查找最新的训练模型')
    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)
    dataset_type = config['data']['dataset_type']

    # 自动查找模型路径（默认启用自动查找）
    if args.model_path is None:
        base_dir = config['output_dir']
        latest_exp_dir = find_latest_experiment_dir(base_dir, dataset_type)

        if latest_exp_dir:
            auto_model_path = os.path.join(latest_exp_dir, 'models', 'best_model.pth')
            if os.path.exists(auto_model_path):
                args.model_path = auto_model_path
                print(f"自动找到模型: {auto_model_path}")
            else:
                print(f"警告: 在 {latest_exp_dir} 中未找到 best_model.pth")

        if args.model_path is None:
            # 使用默认路径
            args.model_path = 'saved_models/best_model.pth'
    elif args.auto_find:
        # 如果明确指定了auto_find，也执行自动查找
        base_dir = config['output_dir']
        latest_exp_dir = find_latest_experiment_dir(base_dir, dataset_type)

        if latest_exp_dir:
            auto_model_path = os.path.join(latest_exp_dir, 'models', 'best_model.pth')
            if os.path.exists(auto_model_path):
                args.model_path = auto_model_path
                print(f"自动找到模型: {auto_model_path}")
            else:
                print(f"警告: 在 {latest_exp_dir} 中未找到 best_model.pth")

    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        print(f"错误: 模型文件不存在: {args.model_path}")
        print("请先训练模型或指定正确的模型路径:")
        print("  python test_simple.py --model_path your_model_path.pth")
        print("或使用 --auto_find 参数自动查找最新模型:")
        print("  python test_simple.py --auto_find")
        return

    # 创建测试输出目录结构
    if args.output_dir is None:
        output_dir = create_test_output_directories(config, args.model_path)
    else:
        output_dir = args.output_dir
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'logs'), exist_ok=True)

    # 设置日志
    logger = setup_logging(os.path.join(output_dir, 'logs'))
    logger.info(f"开始简化测试，配置文件: {args.config}")
    logger.info(f"模型文件: {args.model_path}")
    logger.info(f"数据集类型: {config['data']['dataset_type']}")
    logger.info(f"测试结果目录: {output_dir}")

    # 设置设备
    device = torch.device(config['training']['device'] if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 获取测试数据加载器
    logger.info("加载测试数据...")
    _, _, test_loader = get_data_loaders(config)

    # 加载模型
    logger.info("加载模型...")
    model = load_model(args.model_path, config, device)

    # 测试模型
    logger.info("开始测试...")
    accuracy, macro_f1, kappa, predictions, targets, snrs = test_model(model, test_loader, device, logger)

    # 生成混淆矩阵并保存数据
    logger.info("生成混淆矩阵并保存数据...")
    snr_results = generate_confusion_matrices_and_save_data(predictions, targets, snrs, config, output_dir, logger)

    logger.info(f"测试完成！")
    logger.info(f"总体准确率: {accuracy:.2f}%")
    logger.info(f"Macro-F1: {macro_f1:.2f}%")
    logger.info(f"Kappa: {kappa:.4f}")
    logger.info(f"测试结果保存在: {output_dir}")

if __name__ == '__main__':
    main()
