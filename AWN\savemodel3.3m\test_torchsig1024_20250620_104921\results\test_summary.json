{"overall_metrics": {"accuracy": 65.36682692307693, "macro_f1": 64.50392152091457, "kappa": 0.6392377804487179}, "model_complexity": {"macs": "1.250G", "parameters": "3.702M", "macs_raw": 1250250944.0, "params_raw": 3702247.0}, "inference_performance": {"avg_inference_time_ms": 0.05194886372639583, "std_inference_time_ms": 0.02111060823807958, "min_inference_time_ms": 0.03452599048614502, "max_inference_time_ms": 0.8340626955032349}, "dataset_info": {"total_samples": 208000, "dataset_type": "torchsig1024", "input_shape": [2, 1024], "num_classes": 25, "snr_range": [0.0, 30.0]}, "test_info": {"model_path": "./saved_models/awn/torchsig1024_20250619_064215/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-06-20 10:50:25"}}