# CLDNN模型配置文件 - 支持多种数据集类型

# 模型参数
model:
  type: 'CLDNN'        # 模型类型
  name: 'CLDNN'        # 模型名称
  in_channels: 2       # 输入通道数（I/Q通道）
  num_classes: 26      # 类别数量，会根据数据集自动调整
  sequence_length: 4096 # 序列长度，会根据数据集自动调整

  # 统一的模型参数 - 所有数据集共享
  conv_channels: 128
  lstm_units: 200
  fc_units: 512
  dropout_rate: 0.4
  conv_kernel_size: 8

# 数据参数
data:
  dataset_type: 'rml'   # 可选: 'rml', 'rml201801a', 'hisar', 'torchsig1024', 'torchsig2048', 'torchsig4096'

  # 不同数据集的SNR范围配置
  snr_ranges:
    rml: [-20, 18]      # RML数据集SNR范围
    rml201801a: [-20, 30]  # RML2018.01a数据集SNR范围
    hisar: [-20, 18]    # Hisar数据集SNR范围
    torchsig1024: [0, 30]  # torchsig1024数据集SNR范围
    torchsig2048: [0, 30]  # torchsig2048数据集SNR范围
    torchsig4096: [0, 30]  # torchsig4096数据集SNR范围

  # 信号序列长度配置
  sequence_lengths:
    rml: 128            # RML数据集序列长度
    rml201801a: 1024    # RML2018.01a数据集序列长度
    hisar: 1024         # Hisar数据集序列长度
    torchsig1024: 1024  # torchsig1024数据集序列长度
    torchsig2048: 2048  # torchsig2048数据集序列长度
    torchsig4096: 4096  # torchsig4096数据集序列长度

  # RML数据集配置
  rml_file_path: 'data/RML2016.10a_dict.pkl'
  modulations: null    # null表示使用所有调制类型
  snr_range: [-20, 18]  # SNR范围 [min_snr, max_snr]
  train_ratio: 0.7     # 训练集比例
  stratify_by_snr: true  # 是否按SNR分层抽样
  samples_per_key: null  # 每个(调制类型,SNR)对的样本数量，null表示使用所有样本

  # RML2018.01a数据集配置
  rml201801a_file_path: 'data/GOLD_XYZ_OSC.0001_1024.hdf5'
  rml201801a_modulations: null  # null表示使用所有24种调制类型，或指定列表如: ['BPSK', 'QPSK', '8PSK', '16QAM']
  rml201801a_use_all_snr: true  # 是否使用所有SNR级别的数据，false表示只使用最后4个SNR级别

  # HisarMod数据集配置
  train_path: 'data/hisar/train_data.mat'
  train_labels_path: 'data/hisar/train_labels.csv'
  train_snr_path: 'data/hisar/train_snr.csv'

  test_path: 'data/hisar/test_data.mat'
  test_labels_path: 'data/hisar/test_labels.csv'
  test_snr_path: 'data/hisar/test_snr.csv'

  # TorchSig数据集配置 - 1024样本长度
  torchsig1024_train_path: 'data/torchsig1024/train_data.mat'
  torchsig1024_train_labels_path: 'data/torchsig1024/train_labels.csv'
  torchsig1024_train_snr_path: 'data/torchsig1024/train_snr.csv'

  torchsig1024_test_path: 'data/torchsig1024/test_data.mat'
  torchsig1024_test_labels_path: 'data/torchsig1024/test_labels.csv'
  torchsig1024_test_snr_path: 'data/torchsig1024/test_snr.csv'

  # TorchSig数据集配置 - 2048样本长度
  torchsig2048_train_path: 'data/torchsig2048/train_data.mat'
  torchsig2048_train_labels_path: 'data/torchsig2048/train_labels.csv'
  torchsig2048_train_snr_path: 'data/torchsig2048/train_snr.csv'

  torchsig2048_test_path: 'data/torchsig2048/test_data.mat'
  torchsig2048_test_labels_path: 'data/torchsig2048/test_labels.csv'
  torchsig2048_test_snr_path: 'data/torchsig2048/test_snr.csv'

  # TorchSig数据集配置 - 4096样本长度
  torchsig4096_train_path: 'data/torchsig4096/train_data.mat'
  torchsig4096_train_labels_path: 'data/torchsig4096/train_labels.csv'
  torchsig4096_train_snr_path: 'data/torchsig4096/train_snr.csv'

  torchsig4096_test_path: 'data/torchsig4096/test_data.mat'
  torchsig4096_test_labels_path: 'data/torchsig4096/test_labels.csv'
  torchsig4096_test_snr_path: 'data/torchsig4096/test_snr.csv'

  # 统一的数据集划分比例
  train_ratio: 0.7     # 训练集比例 (70%)
  test_ratio: 0.15     # 测试集比例 (15%)
  val_ratio: 0.15      # 验证集比例 (15%)
  normalize: true      # 是否对信号进行归一化
  label_mapping: true  # 是否对标签进行映射
# 训练参数
training:
  batch_size: 128         # 大内存环境使用更大batch size
  epochs: 100
  learning_rate: 0.000575
  weight_decay: 0.000006
  warmup_epochs: 5        # 学习率预热轮数
  patience: 10            # 学习率调整耐心值
  early_stop_patience: 10 # 早停耐心值
  seed: 4242               # 随机种子
  # 大内存环境数据加载优化
  num_workers: 12         # 大内存环境使用更多线程
  pin_memory: true        # 使用锁页内存
  prefetch_factor: 8      # 大幅增加预取因子
  persistent_workers: true # 使用持久化工作进程
  early_stopping: true    # 是否启用早停
  min_delta: 0.001        # 最小改进阈值
  monitor: 'val_acc'      # 监控指标，可选 'val_acc' 或 'val_loss'
  scheduler: "cosine"     # 学习率调度器类型
  min_lr: 0.00001         # 最小学习
  clip_grad: 1.0          # 梯度裁剪阈值
  device: 'cuda'          # 使用'cuda'或'cpu'

# 输出设置
output_dir: './saved_models/cldnn'

# 类别名称(按索引顺序) - hisar数据集的26种调制类型
hisar_class_names:
  - "BPSK"
  - "QPSK"
  - "8PSK"
  - "16PSK"
  - "32PSK"
  - "64PSK"
  - "4QAM"
  - "8QAM"
  - "16QAM"
  - "32QAM"
  - "64QAM"
  - "128QAM"
  - "256QAM"
  - "2FSK"
  - "4FSK"
  - "8FSK"
  - "16FSK"
  - "4PAM"
  - "8PAM"
  - "16PAM"
  - "AM-DSB"
  - "AM-DSB-SC"
  - "AM-USB"
  - "AM-LSB"
  - "FM"
  - "PM"

# TorchSig数据集的25种调制类型
torchsig_class_names:
  - "BPSK"
  - "QPSK"
  - "8PSK"
  - "16PSK"
  - "32PSK"
  - "64PSK"
  - "16QAM"
  - "32QAM"
  - "64QAM"
  - "256QAM"
  - "2FSK"
  - "4FSK"
  - "8FSK"
  - "16FSK"
  - "4ASK"
  - "8ASK"
  - "16ASK"
  - "32ASK"
  - "64ASK"
  - "AM-DSB"
  - "AM-DSB-SC"
  - "AM-USB"
  - "AM-LSB"
  - "FM"
  - "OOK"

# RML数据集的11种调制类型
rml_class_names:
  - "8PSK"
  - "AM-DSB"
  - "AM-SSB"
  - "BPSK"
  - "CPFSK"
  - "GFSK"
  - "PAM4"
  - "QAM16"
  - "QAM64"
  - "QPSK"
  - "WBFM"

# RML2018.01a数据集的24种调制类型
rml201801a_class_names:
  - "OOK"
  - "4ASK"
  - "8ASK"
  - "BPSK"
  - "QPSK"
  - "8PSK"
  - "16PSK"
  - "32PSK"
  - "16APSK"
  - "32APSK"
  - "64APSK"
  - "128APSK"
  - "16QAM"
  - "32QAM"
  - "64QAM"
  - "128QAM"
  - "256QAM"
  - "AM-SSB-WC"
  - "AM-SSB-SC"
  - "AM-DSB-WC"
  - "AM-DSB-SC"
  - "FM"
  - "GMSK"
  - "OQPSK"
