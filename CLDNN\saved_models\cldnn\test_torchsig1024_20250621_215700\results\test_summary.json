{"overall_metrics": {"accuracy": 66.58509615384615, "macro_f1": 65.91652696975811, "kappa": 0.6519280849358975}, "model_complexity": {"macs": "1.123G", "parameters": "950.489K", "macs_raw": 1122599296.0, "params_raw": 950489.0}, "inference_performance": {"avg_inference_time_ms": 0.15065228365934813, "std_inference_time_ms": 0.012980683046586046, "min_inference_time_ms": 0.13930723071098328, "max_inference_time_ms": 0.836055725812912}, "dataset_info": {"total_samples": 208000, "dataset_type": "torchsig1024", "input_shape": [2, 1024], "num_classes": 25, "snr_range": [0.0, 30.0]}, "test_info": {"model_path": "./saved_models/cldnn/torchsig1024_20250619_045536/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-06-21 21:58:13"}}