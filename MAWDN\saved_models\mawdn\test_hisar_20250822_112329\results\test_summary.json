{"overall_metrics": {"accuracy": 74.11384615384615, "macro_f1": 74.29713637844065, "kappa": 0.730784}, "model_complexity": {"macs": "89.336M", "parameters": "735.720K", "macs_raw": 89335872.0, "params_raw": 735720.0}, "inference_performance": {"avg_inference_time_ms": 0.15260054790056668, "std_inference_time_ms": 0.03734531414464738, "min_inference_time_ms": 0.1209452748298645, "max_inference_time_ms": 1.8739402294158936}, "dataset_info": {"total_samples": 260000, "dataset_type": "hisar", "input_shape": [2, 1024], "num_classes": 26, "snr_range": [-20.0, 18.0]}, "test_info": {"model_path": "./saved_models/mawdn/hisar_20250619_004155/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-08-22 11:25:06"}}