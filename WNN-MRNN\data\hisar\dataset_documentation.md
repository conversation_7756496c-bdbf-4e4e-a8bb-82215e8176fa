# HisarMod数据集说明文档

## 数据集概述

HisarMod数据集是一个用于调制识别的无线信号数据集，包含多种调制类型的IQ信号。数据已被处理为适合深度学习模型训练的格式。

## 文件结构

数据集包含以下主要文件：

- `Train/train_data.mat`: 训练集IQ信号数据
- `Train/train_labels.csv`: 训练集标签
- `Train/train_snr.csv`: 训练集信噪比值
- `Test/test_data.mat`: 测试集IQ信号数据
- `Test/test_labels.csv`: 测试集标签
- `Test/test_snr.csv`: 测试集信噪比值

## 数据格式说明

### MAT文件格式

MAT文件中的数据组织如下：

- 每个MAT文件包含一个名为`data`的变量
- `data`的形状为`(样本数, 2, 1024)`，其中：
  - 第一维表示样本编号，与对应的labels和snr文件中的行号一一对应
  - 第二维大小为2，表示I通道(0)和Q通道(1)
  - 第三维大小为1024，表示每个IQ信号的长度

### 标签编码

标签编码系统如下（与readme.txt一致）：

```
标签映射 = {
    0 : "BPSK",
    10: "QPSK",
    20: "8PSK",
    30: "16PSK",
    40: "32PSK",
    50: "64PSK",
    1 : "4QAM",
    11: "8QAM",
    21: "16QAM",
    31: "32QAM",
    41: "64QAM",
    51: "128QAM",
    61: "256QAM",
    2 : "2FSK",
    12: "4FSK",
    22: "8FSK",
    32: "16FSK",
    3 : "4PAM",
    13: "8PAM",
    23: "16PAM",
    4 : "AM-DSB",
    14: "AM-DSB-SC",
    24: "AM-USB",
    34: "AM-LSB",
    44: "FM",
    54: "PM"
}
```

可以通过以下方式获取调制族和调制类型：
- 调制族标签 = 标签值 % 10
- 调制类型标签 = 标签值 // 10 （整除）

### 信噪比文件

SNR文件中的每一行对应一个样本的信噪比值，单位为dB。这些值对于了解信号质量和训练鲁棒的识别算法非常重要。

## 数据加载示例

以下是使用Python加载和使用数据集的示例代码：

```python
import scipy.io as sio
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader

class HisarModDataset(Dataset):
    def __init__(self, data_path, labels_path, snr_path, transform=None):
        """
        初始化HisarMod数据集
        
        参数:
            data_path (str): MAT文件路径
            labels_path (str): 标签CSV文件路径
            snr_path (str): SNR CSV文件路径
            transform (callable, optional): 可选的数据转换
        """
        # 加载IQ信号数据
        self.data = sio.loadmat(data_path)['data']
        
        # 加载标签
        self.labels = np.loadtxt(labels_path, delimiter=',', dtype=np.int32)
        
        # 加载SNR值
        self.snr = np.loadtxt(snr_path, delimiter=',', dtype=np.float32)
        
        self.transform = transform
        
        # 标签映射
        self.label_map = {
            0: "BPSK", 10: "QPSK", 20: "8PSK", 30: "16PSK", 40: "32PSK", 50: "64PSK",
            1: "4QAM", 11: "8QAM", 21: "16QAM", 31: "32QAM", 41: "64QAM", 
            51: "128QAM", 61: "256QAM", 2: "2FSK", 12: "4FSK", 22: "8FSK", 
            32: "16FSK", 3: "4PAM", 13: "8PAM", 23: "16PAM", 4: "AM-DSB", 
            14: "AM-DSB-SC", 24: "AM-USB", 34: "AM-LSB", 44: "FM", 54: "PM"
        }
    
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        iq_signal = self.data[idx]  # 形状为 (2, 1024)
        label = self.labels[idx]
        snr = self.snr[idx]
        
        # 获取调制族和类型
        family = label % 10
        mod_type = label // 10
        
        # 选择性应用转换
        if self.transform:
            iq_signal = self.transform(iq_signal)
        
        # 转换为PyTorch张量
        iq_signal = torch.from_numpy(iq_signal).float()
        
        return {
            'signal': iq_signal,
            'label': label,
            'snr': snr,
            'family': family,
            'mod_type': mod_type
        }

# 使用示例
def load_data(batch_size=64):
    # 加载训练集
    train_dataset = HisarModDataset(
        data_path='Train/train_data.mat',
        labels_path='Train/train_labels.csv',
        snr_path='Train/train_snr.csv'
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4
    )
    
    # 加载测试集
    test_dataset = HisarModDataset(
        data_path='Test/test_data.mat',
        labels_path='Test/test_labels.csv',
        snr_path='Test/test_snr.csv'
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4
    )
    
    return train_loader, test_loader

# 使用方法示例
train_loader, test_loader = load_data()
print(f"训练集样本数: {len(train_loader.dataset)}")
print(f"测试集样本数: {len(test_loader.dataset)}")

# 查看几个样本
for batch in train_loader:
    print(f"批次形状: {batch['signal'].shape}")  # [batch_size, 2, 1024]
    print(f"标签: {batch['label'][:5]}")
    print(f"信噪比: {batch['snr'][:5]}")
    break
```

## 注意事项

1. 数据的顺序与原始CSV文件一致，确保了标签和SNR值与信号样本的对应关系
2. 每个样本的IQ信号长度统一为1024，如果原始数据不足则用零填充，超出则截断
3. 在使用数据时，建议根据信噪比对数据进行分层分析，因为不同信噪比条件下的识别性能往往差异很大

## 数据可视化建议

要可视化IQ信号，可以使用以下方法：
1. 时域分析：绘制I和Q通道随时间的变化
2. 星座图：将I作为X轴，Q作为Y轴绘制散点图
3. 频域分析：对信号进行FFT变换并绘制功率谱密度

## 参考文献

对于调制识别的更多信息和技术，请参考相关文献和资源。 