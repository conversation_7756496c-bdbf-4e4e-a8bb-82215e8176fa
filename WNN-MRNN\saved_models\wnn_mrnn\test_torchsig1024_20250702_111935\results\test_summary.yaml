dataset_info:
  dataset_type: torchsig1024
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.27122712823060846
  max_inference_time_ms: 1.2196600437164307
  min_inference_time_ms: 0.16719475388526917
  std_inference_time_ms: 0.05045175974305945
model_complexity:
  macs: 988.552M
  macs_raw: 988552448.0
  parameters: 894.969K
  params_raw: 894969.0
overall_metrics:
  accuracy: 66.9826923076923
  kappa: 0.6560697115384615
  macro_f1: 66.24400754583785
test_info:
  config_path: config.yaml
  model_path: ./saved_models/wnn_mrnn/torchsig1024_20250624_023012/models/best_model.pth
  test_date: '2025-07-02 11:22:26'
