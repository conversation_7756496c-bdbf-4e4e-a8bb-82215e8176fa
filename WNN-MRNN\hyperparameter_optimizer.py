#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WNN-MRNN 超参数优化器
专门优化 wavelet_dim, num_levels, rnn_dim, num_layers, dropout, batch_size 六个关键参数
支持显存不足时跳过实验，支持自定义参数选择
"""

# ==================== 配置区域 ====================
# 在这里统一配置所有优化参数，方便修改

# 基本配置
OPTIMIZATION_CONFIG = {
    # 实验基本设置
    'study_name': 'wnn_mrnn_hyperopt',
    'config_path': 'config.yaml',

    # 要优化的数据集列表
    'datasets': ['rml201801a'],

    # 优化模式
    'optimization_mode': 'separate',  # 'separate': 分别优化每个数据集, 'combined': 联合优

    # 训练配置
    'training_epochs': 1,  # 优化时使用的训练轮数
    'early_stop_patience': 1,  # 早停耐心值
    'min_epochs': 1,  # 最少训练轮数，避免过早停止
    'n_trials_per_dataset': 30,  # 每个数据集的试验次数

    # 参数优化配置
    #'optimize_params': ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout', 'batch_size'],  # 要优化的参数列表，可自定义选择
    'optimize_params': ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout'],
    # 要优化的参数列表，可自定义选择
    'skip_on_oom': True,  # 是否在显存不足时跳过实验
    'max_oom_retries': 1,  # 显存不足时的最大重试次数

    # 存储配置
    'save_frequency': 1,  # 每N次试验保存一次结
    'enable_database': True,  # 是否启用数据库存储（支持断点续传）
    'database_url': 'sqlite:///wnn_mrnn_optimization.db',  # 数据库路径
}

# 每个数据集的参数搜索范围
PARAMETER_RANGES = {
    'rml': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [32, 64, 128, 256]
    },
    'rml201801a': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [32, 64, 128, 256]
    },
    'hisar': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [32, 64, 128, 256]
    },
    'torchsig1024': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [32, 64, 128, 256]
    },
    'torchsig2048': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [16, 32, 64, 128]  # 较大序列长度使用较小batch_size
    },
    'torchsig4096': {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
        'dropout': (0.05, 0.5),  # 连续值范围 (最小值, 最大值)
        'batch_size': [8, 16, 32, 64]  # 最大序列长度使用最小batch_size
    }
}

# ==================== 代码实现区域 ====================

import os
import sys
import yaml
import copy
import optuna
import logging
import json
import time
import torch
import gc
from typing import Dict, List, Tuple
import tempfile
import shutil
from datetime import datetime

# 添加当前目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train import main as train_main

def clear_gpu_memory():
    """清理GPU显存"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        gc.collect()

def check_gpu_memory():
    """检查GPU显存使用情况"""
    if torch.cuda.is_available():
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB
        allocated_memory = torch.cuda.memory_allocated(0) / 1024**3  # GB
        cached_memory = torch.cuda.memory_reserved(0) / 1024**3  # GB
        free_memory = total_memory - cached_memory
        return {
            'total': total_memory,
            'allocated': allocated_memory,
            'cached': cached_memory,
            'free': free_memory
        }
    return None

def is_oom_error(exception):
    """判断是否为显存不足错误"""
    error_msg = str(exception).lower()
    oom_keywords = [
        'out of memory',
        'cuda out of memory',
        'runtime error',
        'memory error',
        'allocation failed'
    ]
    return any(keyword in error_msg for keyword in oom_keywords)

class WNNMRNNOptimizer:
    """WNN-MRNN超参数优化器"""

    def __init__(self, config=None):
        """
        初始化优化器

        Args:
            config: 优化配置字典，如果为None则使用全局OPTIMIZATION_CONFIG
        """
        # 使用传入的配置或全局配置
        self.config = config or OPTIMIZATION_CONFIG

        self.config_path = self.config['config_path']
        self.datasets = self.config['datasets']
        self.n_trials = self.config.get('n_trials', 50)  # 默认50次试验
        self.study_name = self.config['study_name']
        self.save_frequency = self.config['save_frequency']

        # 加载基础配置
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.base_config = yaml.safe_load(f)

        # 创建结果保存目录
        self.results_dir = f'optimization_results_{self.study_name}'
        os.makedirs(self.results_dir, exist_ok=True)

        # 设置日志
        log_file = os.path.join(self.results_dir, f'{self.study_name}_optimization.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        # 初始化最佳结果跟踪
        self.best_results_file = os.path.join(self.results_dir, 'current_best_results.json')
        self.all_results_file = os.path.join(self.results_dir, 'all_trial_results.json')
        self.ranked_results_file = os.path.join(self.results_dir, 'ranked_results.json')
        self.load_existing_results()

    def load_existing_results(self):
        """加载已有的优化结果"""
        self.best_result = None
        self.all_results = []

        # 加载最佳结果
        if os.path.exists(self.best_results_file):
            try:
                with open(self.best_results_file, 'r', encoding='utf-8') as f:
                    self.best_result = json.load(f)
                self.logger.info(f"加载已有最佳结果: 准确率 {self.best_result['best_value']:.4f}")
            except Exception as e:
                self.logger.warning(f"加载最佳结果失败: {e}")

        # 加载所有结果
        if os.path.exists(self.all_results_file):
            try:
                with open(self.all_results_file, 'r', encoding='utf-8') as f:
                    self.all_results = json.load(f)
                self.logger.info(f"加载已有试验结果: {len(self.all_results)} 次试验")
            except Exception as e:
                self.logger.warning(f"加载试验历史失败: {e}")

    def save_ranked_results(self):
        """保存按准确率排序的结果"""
        if not self.all_results:
            return

        # 按准确率降序排序
        sorted_results = sorted(self.all_results, key=lambda x: x.get('value', 0), reverse=True)

        # 添加排名信息
        ranked_results = []
        for rank, result in enumerate(sorted_results, 1):
            ranked_result = result.copy()
            ranked_result['rank'] = rank
            ranked_results.append(ranked_result)

        # 保存排序结果
        try:
            with open(self.ranked_results_file, 'w', encoding='utf-8') as f:
                json.dump(ranked_results, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.warning(f"保存排序结果失败: {e}")

    def display_current_ranking(self, top_n=5):
        """显示当前排名情况"""
        if len(self.all_results) < 2:
            return

        # 按准确率排序
        sorted_results = sorted(self.all_results, key=lambda x: x.get('value', 0), reverse=True)

        self.logger.info(f"\n📊 当前TOP {min(top_n, len(sorted_results))} 参数组合排名:")
        self.logger.info("-" * 80)

        for i, result in enumerate(sorted_results[:top_n]):
            rank = i + 1
            trial_num = result.get('trial_number', 'N/A')
            accuracy = result.get('value', 0)
            params = result.get('parameters', {})

            # 格式化参数显示
            param_str = ', '.join([f"{k}={v}" for k, v in params.items()])

            self.logger.info(f"#{rank} | 试验{trial_num} | 准确率:{accuracy:.4f} | {param_str}")

        self.logger.info("-" * 80)

    def get_top_n_results(self, n=10):
        """获取前N个最佳结果"""
        if not self.all_results:
            return []

        sorted_results = sorted(self.all_results, key=lambda x: x.get('value', 0), reverse=True)
        return sorted_results[:n]

    def save_current_results(self, trial_number: int, params: Dict, value: float,
                           individual_results: Dict = None):
        """实时保存当前结果"""
        current_time = datetime.now().isoformat()

        # 保存当前试验结果
        trial_result = {
            'trial_number': trial_number,
            'timestamp': current_time,
            'parameters': params,
            'value': value,
            'individual_results': individual_results or {}
        }

        self.all_results.append(trial_result)

        # 更新最佳结果
        if self.best_result is None or value > self.best_result['best_value']:
            self.best_result = {
                'trial_number': trial_number,
                'timestamp': current_time,
                'best_params': params,
                'best_value': value,
                'individual_results': individual_results or {},
                'datasets': self.datasets,
                'total_trials_so_far': len(self.all_results)
            }

            # 保存最佳结果
            with open(self.best_results_file, 'w', encoding='utf-8') as f:
                json.dump(self.best_result, f, indent=2, ensure_ascii=False)

            self.logger.info(f"🎉 发现新的最佳结果! 试验 {trial_number}, 准确率: {value:.4f}")
            self.logger.info(f"最佳参数: {params}")

        # 实时保存排序结果
        self.save_ranked_results()

        # 定期保存所有结果
        if trial_number % self.save_frequency == 0:
            with open(self.all_results_file, 'w', encoding='utf-8') as f:
                json.dump(self.all_results, f, indent=2, ensure_ascii=False)
            self.logger.info(f"已保存前 {len(self.all_results)} 次试验结果")

        # 显示当前排名情况
        self.display_current_ranking(top_n=5)

    def suggest_parameters(self, trial: optuna.Trial, dataset: str) -> Dict:
        """
        为指定数据集建议超参数

        Args:
            trial: Optuna试验对象
            dataset: 数据集名称

        Returns:
            建议的超参数字典
        """
        # 获取数据集的参数范围
        if dataset not in PARAMETER_RANGES:
            self.logger.warning(f"数据集 {dataset} 未在PARAMETER_RANGES中定义，使用默认范围")
            ranges = PARAMETER_RANGES['rml201801a']  # 使用默认范围
        else:
            ranges = PARAMETER_RANGES[dataset]

        # 获取要优化的参数列表
        optimize_params = self.config.get('optimize_params', ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout', 'batch_size'])

        params = {}

        # 根据配置建议参数
        if 'wavelet_dim' in optimize_params:
            params['wavelet_dim'] = trial.suggest_categorical('wavelet_dim', ranges['wavelet_dim'])

        if 'rnn_dim' in optimize_params:
            params['rnn_dim'] = trial.suggest_categorical('rnn_dim', ranges['rnn_dim'])

        if 'num_levels' in optimize_params:
            params['num_levels'] = trial.suggest_categorical('num_levels', ranges['num_levels'])

        if 'num_layers' in optimize_params:
            params['num_layers'] = trial.suggest_categorical('num_layers', ranges['num_layers'])

        if 'dropout' in optimize_params:
            dropout_range = ranges['dropout']
            if isinstance(dropout_range, tuple) and len(dropout_range) == 2:
                # 连续值范围
                params['dropout'] = trial.suggest_float('dropout', dropout_range[0], dropout_range[1])
            else:
                # 离散值列表（向后兼容）
                params['dropout'] = trial.suggest_categorical('dropout', dropout_range)

        if 'batch_size' in optimize_params:
            params['batch_size'] = trial.suggest_categorical('batch_size', ranges['batch_size'])

        return params

    def print_training_info(self, trial: optuna.Trial, params: Dict, dataset: str):
        """打印训练前的关键信息"""
        self.logger.info("=" * 100)
        self.logger.info(f"🚀 开始训练 - Trial {trial.number} | 数据集: {dataset}")
        self.logger.info("=" * 100)

        # 1. 本次训练的相关参数
        self.logger.info("📋 本次训练参数:")
        param_lines = []
        for key, value in params.items():
            if isinstance(value, float):
                param_lines.append(f"  {key}: {value:.4f}")
            else:
                param_lines.append(f"  {key}: {value}")
        self.logger.info("\n".join(param_lines))

        # 2. 当前TOP参数（如果有历史记录）
        if len(self.all_results) > 0:
            self.logger.info("\n🏆 当前TOP 3最佳参数组合:")
            top_results = self.get_top_n_results(3)
            for i, result in enumerate(top_results):
                rank = i + 1
                trial_num = result.get('trial_number', 'N/A')
                accuracy = result.get('value', 0)
                result_params = result.get('parameters', {})

                param_str = ', '.join([f"{k}={v:.4f}" if isinstance(v, float) else f"{k}={v}"
                                     for k, v in result_params.items()])
                self.logger.info(f"  #{rank} | Trial{trial_num} | 准确率:{accuracy:.4f} | {param_str}")
        else:
            self.logger.info("\n🏆 当前TOP参数: 暂无历史记录")

        # 3. 估算模型参数量（基于参数配置）
        estimated_params = self.estimate_model_parameters(params, dataset)
        self.logger.info(f"\n🔧 预估模型参数量: {estimated_params:,}")

        self.logger.info("=" * 100)
        self.logger.info("🎯 开始训练...")
        self.logger.info("=" * 100)

    def estimate_model_parameters(self, params: Dict, dataset: str) -> int:
        """估算模型参数量"""
        try:
            # 获取基本配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 获取数据集信息
            if dataset == 'rml':
                num_classes = len(config['rml_class_names'])
                sequence_length = config['data']['sequence_lengths']['rml']
            elif dataset == 'rml201801a':
                num_classes = len(config['rml201801a_class_names'])
                sequence_length = config['data']['sequence_lengths']['rml201801a']
            elif dataset == 'hisar':
                num_classes = len(config['hisar_class_names'])
                sequence_length = config['data']['sequence_lengths']['hisar']
            elif dataset.startswith('torchsig'):
                num_classes = len(config['torchsig_class_names'])
                sequence_length = config['data']['sequence_lengths'][dataset]
            else:
                return 0

            # 获取模型参数
            wavelet_dim = params.get('wavelet_dim', 64)
            rnn_dim = params.get('rnn_dim', 64)
            num_layers = params.get('num_layers', 2)
            num_levels = params.get('num_levels', 3)

            # 简化的参数量估算
            # 小波变换部分
            wavelet_params = wavelet_dim * 64 * 3 * num_levels  # 近似估算

            # RNN部分 (MMRNN)
            rnn_params = rnn_dim * rnn_dim * 4 * num_layers * (num_levels + 1)  # 近似估算

            # 分类器部分
            classifier_params = rnn_dim * num_classes

            total_params = wavelet_params + rnn_params + classifier_params
            return int(total_params)

        except Exception as e:
            self.logger.warning(f"参数量估算失败: {e}")
            return 0

    def create_config_with_params(self, dataset: str, params: Dict) -> str:
        """
        创建带有指定参数的临时配置文件

        Args:
            dataset: 数据集名称
            params: 超参数字典

        Returns:
            临时配置文件路径
        """
        # 复制基础配置
        config = copy.deepcopy(self.base_config)

        # 设置数据集
        config['data']['dataset_type'] = dataset

        # 更新模型参数
        if 'dataset_specific_params' not in config['model']:
            config['model']['dataset_specific_params'] = {}

        if dataset not in config['model']['dataset_specific_params']:
            config['model']['dataset_specific_params'][dataset] = {}

        # 设置数据集特定参数（如果在优化参数中）
        if 'wavelet_dim' in params:
            config['model']['dataset_specific_params'][dataset]['wavelet_dim'] = params['wavelet_dim']
        if 'rnn_dim' in params:
            config['model']['dataset_specific_params'][dataset]['rnn_dim'] = params['rnn_dim']
        if 'num_layers' in params:
            config['model']['dataset_specific_params'][dataset]['num_layers'] = params['num_layers']
        if 'num_levels' in params:
            config['model']['dataset_specific_params'][dataset]['num_levels'] = params['num_levels']
        if 'dropout' in params:
            config['model']['dataset_specific_params'][dataset]['dropout'] = params['dropout']
        if 'batch_size' in params:
            config['model']['dataset_specific_params'][dataset]['batch_size'] = params['batch_size']

        # 设置通用参数（如果在优化参数中）
        if 'num_levels' in params:
            config['model']['num_levels'] = params['num_levels']
        if 'num_layers' in params:
            config['model']['num_layers'] = params['num_layers']
        if 'dropout' in params:
            config['model']['dropout'] = params['dropout']

        # 设置训练参数
        if 'batch_size' in params:
            config['training']['batch_size'] = params['batch_size']

        # 使用配置的训练参数以加快优化速度
        config['training']['epochs'] = self.config['training_epochs']
        config['training']['early_stop_patience'] = self.config['early_stop_patience']

        # 创建临时配置文件
        temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(config, temp_config, default_flow_style=False, allow_unicode=True)
        temp_config.close()

        return temp_config.name
    
    def objective(self, trial: optuna.Trial) -> float:
        """
        优化目标函数

        Args:
            trial: Optuna试验对象

        Returns:
            目标值（平均验证准确率）
        """
        total_accuracy = 0.0
        valid_datasets = 0
        individual_results = {}

        for dataset in self.datasets:
            temp_config_path = None
            try:
                # 建议参数
                params = self.suggest_parameters(trial, dataset)

                # 创建临时配置文件
                temp_config_path = self.create_config_with_params(dataset, params)

                self.logger.info(f"Trial {trial.number}: 测试数据集 {dataset}")
                self.logger.info(f"参数: {params}")

                # 运行训练
                oom_retry_count = 0
                max_retries = self.config.get('max_oom_retries', 3)
                skip_on_oom = self.config.get('skip_on_oom', True)

                while oom_retry_count <= max_retries:
                    try:
                        # 清理显存
                        clear_gpu_memory()

                        # 检查显存状态
                        memory_info = check_gpu_memory()
                        if memory_info:
                            self.logger.info(f"显存状态 - 总计: {memory_info['total']:.1f}GB, "
                                           f"已用: {memory_info['allocated']:.1f}GB, "
                                           f"缓存: {memory_info['cached']:.1f}GB, "
                                           f"可用: {memory_info['free']:.1f}GB")

                        # 打印训练前的关键信息
                        self.print_training_info(trial, params, dataset)

                        # 临时修改sys.argv以传递配置文件路径
                        original_argv = sys.argv.copy()
                        sys.argv = ['train.py', '--config', temp_config_path]

                        # 运行训练并获取结果
                        self.logger.info(f"开始训练 {dataset} Trial {trial.number}...")
                        result = train_main()
                        self.logger.info(f"训练完成 {dataset} Trial {trial.number}, 结果: {result}")

                        if result and 'best_val_acc' in result:
                            accuracy = result['best_val_acc']
                            total_accuracy += accuracy
                            valid_datasets += 1
                            individual_results[dataset] = {
                                'accuracy': accuracy,
                                'f1': result.get('best_val_f1', 0),
                                'kappa': result.get('best_val_kappa', 0),
                                'epochs': result.get('total_epochs', 0),
                                'training_time': result.get('total_training_time', 0)
                            }

                            self.logger.info(f"数据集 {dataset} 验证准确率: {accuracy:.4f}")

                            # 报告中间结果用于剪枝
                            trial.report(accuracy, valid_datasets - 1)

                            # 检查是否应该剪枝
                            if trial.should_prune():
                                self.logger.info(f"Trial {trial.number} 被剪枝")
                                raise optuna.TrialPruned()
                        else:
                            self.logger.warning(f"数据集 {dataset} 训练失败，跳过")
                            individual_results[dataset] = {'accuracy': 0, 'error': 'training_failed'}

                        # 训练成功，跳出重试循环
                        break

                    except Exception as e:
                        if is_oom_error(e) and skip_on_oom:
                            oom_retry_count += 1
                            self.logger.warning(f"数据集 {dataset} 显存不足 (重试 {oom_retry_count}/{max_retries}): {str(e)}")

                            # 清理显存
                            clear_gpu_memory()

                            if oom_retry_count > max_retries:
                                self.logger.error(f"数据集 {dataset} 显存不足，已达到最大重试次数，跳过此参数组合")
                                individual_results[dataset] = {'accuracy': 0, 'error': 'out_of_memory'}
                                break
                            else:
                                # 等待一段时间再重试
                                time.sleep(2)
                                continue
                        else:
                            # 非显存错误，直接抛出
                            self.logger.error(f"数据集 {dataset} 训练出现错误: {str(e)}")
                            individual_results[dataset] = {'accuracy': 0, 'error': str(e)}
                            break

                    finally:
                        # 恢复原始argv
                        sys.argv = original_argv

                        # 清理显存
                        clear_gpu_memory()

            except Exception as e:
                self.logger.error(f"数据集 {dataset} 优化失败: {str(e)}")
                individual_results[dataset] = {'accuracy': 0, 'error': str(e)}
                continue
            finally:
                # 确保清理临时文件
                if temp_config_path and os.path.exists(temp_config_path):
                    try:
                        os.unlink(temp_config_path)
                        self.logger.debug(f"已清理临时配置文件: {temp_config_path}")
                    except Exception as e:
                        self.logger.warning(f"清理临时文件失败: {e}")
        
        if valid_datasets == 0:
            raise optuna.TrialPruned("所有数据集都失败")
        
        # 返回平均准确率
        avg_accuracy = total_accuracy / valid_datasets
        self.logger.info(f"Trial {trial.number} 平均准确率: {avg_accuracy:.4f}")

        # 实时保存结果 - 获取实际使用的参数
        actual_params = {}
        optimize_params = self.config.get('optimize_params', ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout', 'batch_size'])

        # 只保存实际优化的参数
        for param in optimize_params:
            if param in trial.params:
                actual_params[param] = trial.params[param]

        self.save_current_results(trial.number, actual_params, avg_accuracy, individual_results)

        return avg_accuracy
    
    def optimize(self) -> Dict:
        """
        执行超参数优化
        
        Returns:
            最佳参数和结果
        """
        self.logger.info(f"开始优化，目标数据集: {self.datasets}")
        self.logger.info(f"优化试验次数: {self.n_trials}")
        self.logger.info(f"结果保存目录: {self.results_dir}")

        # 显示当前最佳结果（如果有）
        if self.best_result:
            self.logger.info(f"当前最佳结果: 准确率 {self.best_result['best_value']:.4f}")
            self.logger.info(f"当前最佳参数: {self.best_result['best_params']}")

        # 创建研究（支持断点续传）
        storage_url = None
        if self.config.get('enable_database', False):
            storage_url = self.config.get('database_url', 'sqlite:///wnn_mrnn_optimization.db')
            self.logger.info(f"启用数据库存储: {storage_url}")

        study = optuna.create_study(
            direction='maximize',
            study_name=self.study_name,
            storage=storage_url,
            load_if_exists=True,  # 如果研究已存在则加载
            pruner=optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=10)
        )
        
        # 执行优化
        try:
            study.optimize(self.objective, n_trials=self.n_trials)
        except KeyboardInterrupt:
            self.logger.info("优化被用户中断，保存当前结果...")
        except Exception as e:
            self.logger.error(f"优化过程中出现错误: {e}")

        # 最终保存所有结果
        with open(self.all_results_file, 'w', encoding='utf-8') as f:
            json.dump(self.all_results, f, indent=2, ensure_ascii=False)

        # 获取最终结果
        if hasattr(study, 'best_params') and study.best_params:
            best_params = study.best_params
            best_value = study.best_value
        elif self.best_result:
            best_params = self.best_result['best_params']
            best_value = self.best_result['best_value']
        else:
            self.logger.warning("未找到有效的优化结果")
            return None

        self.logger.info(f"优化完成！")
        self.logger.info(f"最佳参数: {best_params}")
        self.logger.info(f"最佳平均准确率: {best_value:.4f}")
        self.logger.info(f"总共完成 {len(self.all_results)} 次试验")

        # 保存最终详细结果
        final_results = {
            'best_params': best_params,
            'best_value': best_value,
            'datasets': self.datasets,
            'n_trials': self.n_trials,
            'completed_trials': len(self.all_results),
            'study_name': self.study_name,
            'optimization_config': self.config,
            'parameter_ranges': {dataset: PARAMETER_RANGES.get(dataset, {}) for dataset in self.datasets},
            'completion_time': datetime.now().isoformat()
        }

        # 保存为YAML格式
        results_file = os.path.join(self.results_dir, 'final_optimization_results.yaml')
        with open(results_file, 'w', encoding='utf-8') as f:
            yaml.dump(final_results, f, default_flow_style=False, allow_unicode=True)

        # 保存为JSON格式（更详细）
        results_json_file = os.path.join(self.results_dir, 'final_optimization_results.json')
        with open(results_json_file, 'w', encoding='utf-8') as f:
            json.dump(final_results, f, indent=2, ensure_ascii=False)

        self.logger.info(f"最终结果已保存到:")
        self.logger.info(f"  YAML格式: {results_file}")
        self.logger.info(f"  JSON格式: {results_json_file}")
        self.logger.info(f"  实时最佳: {self.best_results_file}")
        self.logger.info(f"  实时排名: {self.ranked_results_file}")
        self.logger.info(f"  所有试验: {self.all_results_file}")

        # 显示最终排名
        self.logger.info(f"\n🏆 最终TOP 10参数排名:")
        top_results = self.get_top_n_results(10)
        for i, result in enumerate(top_results):
            rank = i + 1
            trial_num = result.get('trial_number', 'N/A')
            accuracy = result.get('value', 0)
            params = result.get('parameters', {})

            if rank <= 3:
                medals = {1: "🥇", 2: "🥈", 3: "🥉"}
                rank_str = f"{medals.get(rank, '')} #{rank}"
            else:
                rank_str = f"   #{rank}"

            param_str = ', '.join([f"{k}={v}" for k, v in params.items()])
            self.logger.info(f"{rank_str} | 试验{trial_num} | {accuracy:.4f} | {param_str}")

        self.logger.info(f"\n💡 快速查看排名: python quick_ranking.py")
        self.logger.info(f"💡 详细结果分析: python view_optimization_results.py")

        return final_results


class WNNMRNNSeparateOptimizer:
    """WNN-MRNN分别优化器 - 为每个数据集单独寻找最优参数"""

    def __init__(self, config=None):
        """初始化分别优化器"""
        self.config = config or OPTIMIZATION_CONFIG
        self.config_path = self.config['config_path']
        self.datasets = self.config['datasets']
        self.n_trials_per_dataset = self.config['n_trials_per_dataset']
        self.study_name = self.config['study_name']
        self.save_frequency = self.config['save_frequency']

        # 加载基础配置
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.base_config = yaml.safe_load(f)

        # 创建结果保存目录
        self.results_dir = f'optimization_results_{self.study_name}_separate'
        os.makedirs(self.results_dir, exist_ok=True)

        # 设置日志
        log_file = os.path.join(self.results_dir, f'{self.study_name}_separate_optimization.log')
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        # 为每个数据集初始化结果跟踪
        self.dataset_results = {}
        self.all_results = {}  # 按数据集分组的所有结果

        for dataset in self.datasets:
            self.dataset_results[dataset] = {
                'best_result': None,
                'all_trials': [],
                'best_file': os.path.join(self.results_dir, f'{dataset}_best_results.json'),
                'all_file': os.path.join(self.results_dir, f'{dataset}_all_results.json'),
                'ranked_file': os.path.join(self.results_dir, f'{dataset}_ranked_results.json')
            }
            self.all_results[dataset] = []
            self.load_existing_results_for_dataset(dataset)

    def load_existing_results_for_dataset(self, dataset):
        """加载指定数据集的已有结果"""
        dataset_info = self.dataset_results[dataset]

        # 加载最佳结果
        if os.path.exists(dataset_info['best_file']):
            try:
                with open(dataset_info['best_file'], 'r', encoding='utf-8') as f:
                    dataset_info['best_result'] = json.load(f)
                self.logger.info(f"加载 {dataset} 已有最佳结果: 准确率 {dataset_info['best_result']['best_value']:.4f}")
            except Exception as e:
                self.logger.warning(f"加载 {dataset} 最佳结果失败: {e}")

        # 加载所有结果
        if os.path.exists(dataset_info['all_file']):
            try:
                with open(dataset_info['all_file'], 'r', encoding='utf-8') as f:
                    dataset_info['all_trials'] = json.load(f)
                    self.all_results[dataset] = dataset_info['all_trials']
                self.logger.info(f"加载 {dataset} 已有试验结果: {len(dataset_info['all_trials'])} 次试验")
            except Exception as e:
                self.logger.warning(f"加载 {dataset} 试验历史失败: {e}")

    def save_dataset_results(self, dataset, trial_number, params, value):
        """保存指定数据集的结果"""
        current_time = datetime.now().isoformat()
        dataset_info = self.dataset_results[dataset]

        # 保存当前试验结果
        trial_result = {
            'trial_number': trial_number,
            'timestamp': current_time,
            'parameters': params,
            'value': value,
            'dataset': dataset
        }

        dataset_info['all_trials'].append(trial_result)
        self.all_results[dataset].append(trial_result)

        # 更新最佳结果
        if dataset_info['best_result'] is None or value > dataset_info['best_result']['best_value']:
            dataset_info['best_result'] = {
                'trial_number': trial_number,
                'timestamp': current_time,
                'best_params': params,
                'best_value': value,
                'dataset': dataset,
                'total_trials_so_far': len(dataset_info['all_trials'])
            }

            # 保存最佳结果
            with open(dataset_info['best_file'], 'w', encoding='utf-8') as f:
                json.dump(dataset_info['best_result'], f, indent=2, ensure_ascii=False)

            self.logger.info(f"🎉 {dataset} 发现新的最佳结果! 试验 {trial_number}, 准确率: {value:.4f}")
            self.logger.info(f"{dataset} 最佳参数: {params}")

        # 保存排序结果
        self.save_ranked_results_for_dataset(dataset)

        # 定期保存所有结果
        if trial_number % self.save_frequency == 0:
            with open(dataset_info['all_file'], 'w', encoding='utf-8') as f:
                json.dump(dataset_info['all_trials'], f, indent=2, ensure_ascii=False)

    def save_ranked_results_for_dataset(self, dataset):
        """保存指定数据集的排序结果"""
        dataset_info = self.dataset_results[dataset]
        if not dataset_info['all_trials']:
            return

        # 按准确率降序排序
        sorted_results = sorted(dataset_info['all_trials'], key=lambda x: x.get('value', 0), reverse=True)

        # 添加排名信息
        ranked_results = []
        for rank, result in enumerate(sorted_results, 1):
            ranked_result = result.copy()
            ranked_result['rank'] = rank
            ranked_results.append(ranked_result)

        # 保存排序结果
        try:
            with open(dataset_info['ranked_file'], 'w', encoding='utf-8') as f:
                json.dump(ranked_results, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.warning(f"保存 {dataset} 排序结果失败: {e}")

    def display_current_ranking_for_dataset(self, dataset, top_n=5):
        """显示指定数据集的当前排名"""
        dataset_info = self.dataset_results[dataset]
        if len(dataset_info['all_trials']) < 2:
            return

        # 按准确率排序
        sorted_results = sorted(dataset_info['all_trials'], key=lambda x: x.get('value', 0), reverse=True)

        self.logger.info(f"\n📊 {dataset} 当前TOP {min(top_n, len(sorted_results))} 参数组合排名:")
        self.logger.info("-" * 80)

        for i, result in enumerate(sorted_results[:top_n]):
            rank = i + 1
            trial_num = result.get('trial_number', 'N/A')
            accuracy = result.get('value', 0)
            params = result.get('parameters', {})

            # 格式化参数显示
            param_str = ', '.join([f"{k}={v}" for k, v in params.items()])

            self.logger.info(f"#{rank} | 试验{trial_num} | 准确率:{accuracy:.4f} | {param_str}")

        self.logger.info("-" * 80)

    def optimize_single_dataset(self, dataset):
        """优化单个数据集"""
        self.logger.info(f"\n🎯 开始优化数据集: {dataset}")
        self.logger.info(f"试验次数: {self.n_trials_per_dataset}")

        # 显示当前最佳结果（如果有）
        dataset_info = self.dataset_results[dataset]
        if dataset_info['best_result']:
            self.logger.info(f"{dataset} 当前最佳结果: 准确率 {dataset_info['best_result']['best_value']:.4f}")
            self.logger.info(f"{dataset} 当前最佳参数: {dataset_info['best_result']['best_params']}")

        # 创建研究
        storage_url = None
        if self.config.get('enable_database', False):
            storage_url = self.config.get('database_url', 'sqlite:///wnn_mrnn_optimization.db')

        study_name = f"{self.study_name}_{dataset}"
        study = optuna.create_study(
            direction='maximize',
            study_name=study_name,
            storage=storage_url,
            load_if_exists=True,
            pruner=optuna.pruners.MedianPruner(n_startup_trials=3, n_warmup_steps=5)
        )

        # 定义目标函数
        def objective(trial):
            # 建议参数
            if dataset not in PARAMETER_RANGES:
                ranges = PARAMETER_RANGES['rml201801a']  # 使用默认范围
            else:
                ranges = PARAMETER_RANGES[dataset]

            # 获取要优化的参数列表
            optimize_params = self.config.get('optimize_params', ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout', 'batch_size'])

            params = {}

            # 根据配置建议参数
            if 'wavelet_dim' in optimize_params:
                params['wavelet_dim'] = trial.suggest_categorical('wavelet_dim', ranges['wavelet_dim'])

            if 'rnn_dim' in optimize_params:
                params['rnn_dim'] = trial.suggest_categorical('rnn_dim', ranges['rnn_dim'])

            if 'num_levels' in optimize_params:
                params['num_levels'] = trial.suggest_categorical('num_levels', ranges['num_levels'])

            if 'num_layers' in optimize_params:
                params['num_layers'] = trial.suggest_categorical('num_layers', ranges['num_layers'])

            if 'dropout' in optimize_params:
                dropout_range = ranges['dropout']
                if isinstance(dropout_range, tuple) and len(dropout_range) == 2:
                    # 连续值范围
                    params['dropout'] = trial.suggest_float('dropout', dropout_range[0], dropout_range[1])
                else:
                    # 离散值列表（向后兼容）
                    params['dropout'] = trial.suggest_categorical('dropout', dropout_range)

            if 'batch_size' in optimize_params:
                params['batch_size'] = trial.suggest_categorical('batch_size', ranges['batch_size'])

            # 创建临时配置文件
            temp_config_path = self.create_config_with_params(dataset, params)

            self.logger.info(f"{dataset} Trial {trial.number}: 参数 {params}")

            try:
                # 显存不足处理
                oom_retry_count = 0
                max_retries = self.config.get('max_oom_retries', 3)
                skip_on_oom = self.config.get('skip_on_oom', True)

                while oom_retry_count <= max_retries:
                    try:
                        # 清理显存
                        clear_gpu_memory()

                        # 检查显存状态
                        memory_info = check_gpu_memory()
                        if memory_info:
                            self.logger.info(f"显存状态 - 总计: {memory_info['total']:.1f}GB, "
                                           f"已用: {memory_info['allocated']:.1f}GB, "
                                           f"缓存: {memory_info['cached']:.1f}GB, "
                                           f"可用: {memory_info['free']:.1f}GB")

                        # 打印训练前的关键信息
                        self.print_training_info_for_dataset(trial, params, dataset)

                        # 运行训练
                        original_argv = sys.argv.copy()
                        sys.argv = ['train.py', '--config', temp_config_path]

                        self.logger.info(f"开始训练 {dataset} Trial {trial.number}...")
                        result = train_main()
                        self.logger.info(f"训练完成 {dataset} Trial {trial.number}, 结果: {result}")

                        if result and 'best_val_acc' in result:
                            accuracy = result['best_val_acc']
                            self.logger.info(f"{dataset} Trial {trial.number} 验证准确率: {accuracy:.4f}")

                            # 保存结果
                            self.save_dataset_results(dataset, trial.number, params, accuracy)

                            # 显示当前排名
                            if len(dataset_info['all_trials']) >= 2:
                                self.display_current_ranking_for_dataset(dataset, top_n=3)

                            return accuracy
                        else:
                            self.logger.warning(f"{dataset} Trial {trial.number} 训练失败")
                            return 0.0

                        # 训练成功，跳出重试循环
                        break

                    except Exception as e:
                        if is_oom_error(e) and skip_on_oom:
                            oom_retry_count += 1
                            self.logger.warning(f"{dataset} Trial {trial.number} 显存不足 (重试 {oom_retry_count}/{max_retries}): {str(e)}")

                            # 清理显存
                            clear_gpu_memory()

                            if oom_retry_count > max_retries:
                                self.logger.error(f"{dataset} Trial {trial.number} 显存不足，已达到最大重试次数，跳过此参数组合")
                                return 0.0
                            else:
                                # 等待一段时间再重试
                                time.sleep(2)
                                continue
                        else:
                            # 非显存错误，直接返回
                            self.logger.error(f"{dataset} Trial {trial.number} 出现错误: {e}")
                            return 0.0

                    finally:
                        sys.argv = original_argv
                        # 清理显存
                        clear_gpu_memory()

                return 0.0

            finally:
                # 确保清理临时文件
                if temp_config_path and os.path.exists(temp_config_path):
                    try:
                        os.unlink(temp_config_path)
                        self.logger.debug(f"已清理临时配置文件: {temp_config_path}")
                    except Exception as e:
                        self.logger.warning(f"清理临时文件失败: {e}")

        # 执行优化
        try:
            study.optimize(objective, n_trials=self.n_trials_per_dataset)
        except KeyboardInterrupt:
            self.logger.info(f"{dataset} 优化被用户中断，保存当前结果...")
        except Exception as e:
            self.logger.error(f"{dataset} 优化过程中出现错误: {e}")

        # 最终保存结果
        with open(dataset_info['all_file'], 'w', encoding='utf-8') as f:
            json.dump(dataset_info['all_trials'], f, indent=2, ensure_ascii=False)

        return dataset_info['best_result']

    def print_training_info_for_dataset(self, trial: optuna.Trial, params: Dict, dataset: str):
        """打印数据集训练前的关键信息"""
        dataset_info = self.dataset_results[dataset]

        self.logger.info("=" * 100)
        self.logger.info(f"🚀 开始训练 - {dataset} Trial {trial.number}")
        self.logger.info("=" * 100)

        # 1. 本次训练的相关参数
        self.logger.info("📋 本次训练参数:")
        param_lines = []
        for key, value in params.items():
            if isinstance(value, float):
                param_lines.append(f"  {key}: {value:.4f}")
            else:
                param_lines.append(f"  {key}: {value}")
        self.logger.info("\n".join(param_lines))

        # 2. 当前数据集的TOP参数（如果有历史记录）
        if len(dataset_info['all_trials']) > 0:
            self.logger.info(f"\n🏆 {dataset} 当前TOP 3最佳参数组合:")
            sorted_results = sorted(dataset_info['all_trials'], key=lambda x: x.get('value', 0), reverse=True)
            top_results = sorted_results[:3]

            for i, result in enumerate(top_results):
                rank = i + 1
                trial_num = result.get('trial_number', 'N/A')
                accuracy = result.get('value', 0)
                result_params = result.get('parameters', {})

                param_str = ', '.join([f"{k}={v:.4f}" if isinstance(v, float) else f"{k}={v}"
                                     for k, v in result_params.items()])
                self.logger.info(f"  #{rank} | Trial{trial_num} | 准确率:{accuracy:.4f} | {param_str}")
        else:
            self.logger.info(f"\n🏆 {dataset} 当前TOP参数: 暂无历史记录")

        # 3. 估算模型参数量（基于参数配置）
        estimated_params = self.estimate_model_parameters_for_dataset(params, dataset)
        self.logger.info(f"\n🔧 预估模型参数量: {estimated_params:,}")

        self.logger.info("=" * 100)
        self.logger.info("🎯 开始训练...")
        self.logger.info("=" * 100)

    def estimate_model_parameters_for_dataset(self, params: Dict, dataset: str) -> int:
        """估算指定数据集的模型参数量"""
        try:
            # 获取数据集信息
            if dataset == 'rml':
                num_classes = len(self.base_config['rml_class_names'])
                sequence_length = self.base_config['data']['sequence_lengths']['rml']
            elif dataset == 'rml201801a':
                num_classes = len(self.base_config['rml201801a_class_names'])
                sequence_length = self.base_config['data']['sequence_lengths']['rml201801a']
            elif dataset == 'hisar':
                num_classes = len(self.base_config['hisar_class_names'])
                sequence_length = self.base_config['data']['sequence_lengths']['hisar']
            elif dataset.startswith('torchsig'):
                num_classes = len(self.base_config['torchsig_class_names'])
                sequence_length = self.base_config['data']['sequence_lengths'][dataset]
            else:
                return 0

            # 获取模型参数
            wavelet_dim = params.get('wavelet_dim', 64)
            rnn_dim = params.get('rnn_dim', 64)
            num_layers = params.get('num_layers', 2)
            num_levels = params.get('num_levels', 3)

            # 简化的参数量估算
            # 小波变换部分
            wavelet_params = wavelet_dim * 64 * 3 * num_levels  # 近似估算

            # RNN部分 (MMRNN)
            rnn_params = rnn_dim * rnn_dim * 4 * num_layers * (num_levels + 1)  # 近似估算

            # 分类器部分
            classifier_params = rnn_dim * num_classes

            total_params = wavelet_params + rnn_params + classifier_params
            return int(total_params)

        except Exception as e:
            self.logger.warning(f"参数量估算失败: {e}")
            return 0

    def create_config_with_params(self, dataset: str, params: Dict) -> str:
        """创建带有指定参数的临时配置文件"""
        # 复制基础配置
        config = copy.deepcopy(self.base_config)

        # 设置数据集
        config['data']['dataset_type'] = dataset

        # 更新模型参数
        if 'dataset_specific_params' not in config['model']:
            config['model']['dataset_specific_params'] = {}

        if dataset not in config['model']['dataset_specific_params']:
            config['model']['dataset_specific_params'][dataset] = {}

        # 设置数据集特定参数（如果在优化参数中）
        if 'wavelet_dim' in params:
            config['model']['dataset_specific_params'][dataset]['wavelet_dim'] = params['wavelet_dim']
        if 'rnn_dim' in params:
            config['model']['dataset_specific_params'][dataset]['rnn_dim'] = params['rnn_dim']
        if 'num_layers' in params:
            config['model']['dataset_specific_params'][dataset]['num_layers'] = params['num_layers']
        if 'num_levels' in params:
            config['model']['dataset_specific_params'][dataset]['num_levels'] = params['num_levels']
        if 'dropout' in params:
            config['model']['dataset_specific_params'][dataset]['dropout'] = params['dropout']
        if 'batch_size' in params:
            config['model']['dataset_specific_params'][dataset]['batch_size'] = params['batch_size']

        # 设置通用参数（如果在优化参数中）
        if 'num_levels' in params:
            config['model']['num_levels'] = params['num_levels']
        if 'num_layers' in params:
            config['model']['num_layers'] = params['num_layers']
        if 'dropout' in params:
            config['model']['dropout'] = params['dropout']

        # 设置训练参数
        if 'batch_size' in params:
            config['training']['batch_size'] = params['batch_size']

        # 使用配置的训练参数以加快优化速度
        config['training']['epochs'] = self.config['training_epochs']
        config['training']['early_stop_patience'] = self.config['early_stop_patience']

        # 设置最小训练轮数，确保有足够的训练
        if 'min_epochs' in self.config:
            config['training']['min_epochs'] = self.config['min_epochs']

        # 创建临时配置文件
        temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(config, temp_config, default_flow_style=False, allow_unicode=True)
        temp_config.close()

        return temp_config.name

    def optimize(self):
        """执行所有数据集的分别优化"""
        self.logger.info(f"🚀 开始分别优化模式")
        self.logger.info(f"目标数据集: {self.datasets}")
        self.logger.info(f"每个数据集试验次数: {self.n_trials_per_dataset}")
        self.logger.info(f"总试验次数: {len(self.datasets)} × {self.n_trials_per_dataset} = {len(self.datasets) * self.n_trials_per_dataset}")
        self.logger.info(f"结果保存目录: {self.results_dir}")

        all_best_results = {}

        # 为每个数据集分别优化
        for i, dataset in enumerate(self.datasets, 1):
            self.logger.info(f"\n{'='*80}")
            self.logger.info(f"📊 进度: {i}/{len(self.datasets)} - 正在优化数据集: {dataset}")
            self.logger.info(f"{'='*80}")

            best_result = self.optimize_single_dataset(dataset)
            all_best_results[dataset] = best_result

            if best_result:
                self.logger.info(f"✅ {dataset} 优化完成!")
                self.logger.info(f"   最佳准确率: {best_result['best_value']:.4f}")
                self.logger.info(f"   最佳参数: {best_result['best_params']}")
            else:
                self.logger.warning(f"❌ {dataset} 优化失败")

        # 保存汇总结果
        self.save_summary_results(all_best_results)

        # 显示最终汇总
        self.display_final_summary(all_best_results)

        return all_best_results

    def save_summary_results(self, all_best_results):
        """保存汇总结果"""
        summary = {
            'optimization_mode': 'separate',
            'datasets': self.datasets,
            'n_trials_per_dataset': self.n_trials_per_dataset,
            'total_trials': len(self.datasets) * self.n_trials_per_dataset,
            'study_name': self.study_name,
            'optimization_config': self.config,
            'completion_time': datetime.now().isoformat(),
            'best_results_by_dataset': all_best_results
        }

        # 保存为YAML格式
        summary_file = os.path.join(self.results_dir, 'optimization_summary.yaml')
        with open(summary_file, 'w', encoding='utf-8') as f:
            yaml.dump(summary, f, default_flow_style=False, allow_unicode=True)

        # 保存为JSON格式
        summary_json_file = os.path.join(self.results_dir, 'optimization_summary.json')
        with open(summary_json_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        self.logger.info(f"汇总结果已保存到:")
        self.logger.info(f"  YAML格式: {summary_file}")
        self.logger.info(f"  JSON格式: {summary_json_file}")

    def display_final_summary(self, all_best_results):
        """显示最终汇总"""
        self.logger.info(f"\n🏆 分别优化最终结果汇总")
        self.logger.info("="*100)

        for dataset, best_result in all_best_results.items():
            if best_result:
                params = best_result['best_params']
                accuracy = best_result['best_value']
                trials = best_result['total_trials_so_far']

                self.logger.info(f"\n📊 {dataset}:")
                self.logger.info(f"   最佳准确率: {accuracy:.4f}")
                self.logger.info(f"   完成试验数: {trials}")
                self.logger.info(f"   最佳参数: wavelet_dim={params['wavelet_dim']}, rnn_dim={params['rnn_dim']}, num_levels={params['num_levels']}, num_layers={params['num_layers']}")
            else:
                self.logger.info(f"\n❌ {dataset}: 优化失败")

        self.logger.info(f"\n💡 查看各数据集详细结果:")
        for dataset in self.datasets:
            self.logger.info(f"   {dataset}: python quick_ranking.py {dataset}")

        self.logger.info(f"\n📁 结果文件位置: {self.results_dir}/")


def print_current_config():
    """打印当前配置"""
    print("=" * 60)
    print("当前优化配置:")
    print("=" * 60)
    print(f"研究名称: {OPTIMIZATION_CONFIG['study_name']}")
    print(f"优化模式: {'分别优化' if OPTIMIZATION_CONFIG.get('optimization_mode') == 'separate' else '联合优化'}")
    print(f"目标数据集: {OPTIMIZATION_CONFIG['datasets']}")

    if OPTIMIZATION_CONFIG.get('optimization_mode') == 'separate':
        print(f"每个数据集试验次数: {OPTIMIZATION_CONFIG['n_trials_per_dataset']}")
        print(f"总试验次数: {len(OPTIMIZATION_CONFIG['datasets'])} × {OPTIMIZATION_CONFIG['n_trials_per_dataset']} = {len(OPTIMIZATION_CONFIG['datasets']) * OPTIMIZATION_CONFIG['n_trials_per_dataset']}")
        # 估算时间 - 分别优化
        estimated_time_per_trial = OPTIMIZATION_CONFIG['training_epochs'] * 0.5  # 每个数据集单独训练
        total_estimated_time = estimated_time_per_trial * len(OPTIMIZATION_CONFIG['datasets']) * OPTIMIZATION_CONFIG['n_trials_per_dataset']
    else:
        print(f"试验次数: {OPTIMIZATION_CONFIG.get('n_trials', 50)}")
        # 估算时间 - 联合优化
        estimated_time_per_trial = OPTIMIZATION_CONFIG['training_epochs'] * len(OPTIMIZATION_CONFIG['datasets']) * 0.5
        total_estimated_time = estimated_time_per_trial * OPTIMIZATION_CONFIG.get('n_trials', 50)

    print(f"训练轮数: {OPTIMIZATION_CONFIG['training_epochs']} (最少 {OPTIMIZATION_CONFIG.get('min_epochs', 5)} 轮)")
    print(f"早停耐心: {OPTIMIZATION_CONFIG['early_stop_patience']} 轮")
    print(f"保存频率: 每 {OPTIMIZATION_CONFIG['save_frequency']} 次试验")
    print(f"数据库存储: {'启用' if OPTIMIZATION_CONFIG['enable_database'] else '禁用'}")
    print(f"预估总时间: {total_estimated_time:.0f} 分钟 ({total_estimated_time/60:.1f} 小时)")

    print(f"要优化的参数: {OPTIMIZATION_CONFIG.get('optimize_params', ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers'])}")
    print(f"显存不足处理: {'启用' if OPTIMIZATION_CONFIG.get('skip_on_oom', True) else '禁用'}")
    print(f"最大重试次数: {OPTIMIZATION_CONFIG.get('max_oom_retries', 3)}")

    print("\n参数搜索范围:")
    for dataset in OPTIMIZATION_CONFIG['datasets']:
        if dataset in PARAMETER_RANGES:
            ranges = PARAMETER_RANGES[dataset]
            print(f"  {dataset}:")
            optimize_params = OPTIMIZATION_CONFIG.get('optimize_params', ['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers', 'dropout', 'batch_size'])
            for param in optimize_params:
                if param in ranges:
                    param_range = ranges[param]
                    if isinstance(param_range, tuple) and len(param_range) == 2:
                        print(f"    {param}: {param_range[0]:.3f} - {param_range[1]:.3f} (连续值)")
                    else:
                        print(f"    {param}: {param_range}")
    print("=" * 60)

def main():
    """主函数"""
    print("WNN-MRNN 超参数优化器")
    print_current_config()

    # 询问是否继续
    response = input("\n是否使用上述配置开始优化? (y/n): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("优化已取消。请修改文件顶部的配置后重新运行。")
        return

    # 根据优化模式选择优化器
    optimization_mode = OPTIMIZATION_CONFIG.get('optimization_mode', 'separate')

    if optimization_mode == 'separate':
        print("\n🎯 使用分别优化模式 - 为每个数据集单独寻找最优参数")
        optimizer = WNNMRNNSeparateOptimizer()
        results = optimizer.optimize()

        if results:
            print("\n" + "="*80)
            print("🎉 分别优化完成！")
            print("="*80)

            for dataset, result in results.items():
                if result:
                    print(f"\n📊 {dataset}:")
                    print(f"   最佳准确率: {result['best_value']:.4f}")
                    print(f"   最佳参数: {result['best_params']}")
                else:
                    print(f"\n❌ {dataset}: 优化失败")

            print(f"\n📁 结果保存目录: optimization_results_{OPTIMIZATION_CONFIG['study_name']}_separate")
            print("\n📋 应用建议:")
            print("1. 查看各数据集详细结果:")
            for dataset in OPTIMIZATION_CONFIG['datasets']:
                print(f"   - {dataset}: {dataset}_best_results.json, {dataset}_ranked_results.json")
            print("2. 根据需要选择合适的参数应用到 config.yaml 文件")
            print("3. 使用完整训练轮数重新训练模型")
        else:
            print("分别优化失败，请检查日志文件获取详细信息。")

    else:  # combined mode
        print("\n🎯 使用联合优化模式 - 寻找在所有数据集上平均表现最好的参数")
        optimizer = WNNMRNNOptimizer()
        results = optimizer.optimize()

        if results:
            print("\n" + "="*60)
            print("🎉 联合优化完成！")
            print("="*60)
            print(f"最佳参数: {results['best_params']}")
            print(f"最佳平均准确率: {results['best_value']:.4f}")
            print(f"完成试验数: {results['completed_trials']}/{results['n_trials']}")
            print(f"结果保存目录: optimization_results_{results['study_name']}")
            print("="*60)

            print("\n📋 应用建议:")
            print("1. 查看详细结果文件:")
            print(f"   - 实时最佳结果: optimization_results_{results['study_name']}/current_best_results.json")
            print(f"   - 所有试验历史: optimization_results_{results['study_name']}/all_trial_results.json")
            print(f"   - 最终结果: optimization_results_{results['study_name']}/final_optimization_results.yaml")
            print("2. 将最佳参数应用到 config.yaml 文件")
            print("3. 使用完整训练轮数重新训练模型")
        else:
            print("联合优化失败，请检查日志文件获取详细信息。")


if __name__ == '__main__':
    main()
