{"overall_metrics": {"accuracy": 64.23653846153846, "macro_f1": 63.31865966015221, "kappa": 0.6274639423076923}, "model_complexity": {"macs": "8.230M", "parameters": "824.857K", "macs_raw": 8229504.0, "params_raw": 824857.0}, "inference_performance": {"avg_inference_time_ms": 0.0710302132826585, "std_inference_time_ms": 0.02701357889001067, "min_inference_time_ms": 0.03509223461151123, "max_inference_time_ms": 1.1429563164710999}, "dataset_info": {"total_samples": 208000, "dataset_type": "torchsig2048", "input_shape": [2, 2048], "num_classes": 25, "snr_range": [0.0, 30.0]}, "test_info": {"model_path": "saved_models/mamc/torchsig2048_20250609_205402/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-06-21 23:39:59"}}