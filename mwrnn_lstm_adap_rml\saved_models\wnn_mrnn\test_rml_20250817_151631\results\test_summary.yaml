dataset_info:
  dataset_type: rml
  input_shape: !!python/tuple
  - 2
  - 128
  num_classes: 11
  snr_range:
  - -20.0
  - 18.0
  total_samples: 33000
inference_performance:
  avg_inference_time_ms: 0.07740940469684023
  max_inference_time_ms: 0.6712712347507477
  min_inference_time_ms: 0.06019696593284607
  std_inference_time_ms: 0.03214888924762663
model_complexity:
  macs: 18.466M
  macs_raw: 18465728.0
  parameters: 230.603K
  params_raw: 230603.0
overall_metrics:
  accuracy: 62.406060606060606
  kappa: 0.5864666666666667
  macro_f1: 64.24029643839091
test_info:
  config_path: config.yaml
  model_path: ./saved_models/wnn_mrnn/rml_20250817_133109/models/best_model.pth
  test_date: '2025-08-17 15:17:01'
