dataset_info:
  dataset_type: torchsig1024
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.15065228365934813
  max_inference_time_ms: 0.836055725812912
  min_inference_time_ms: 0.13930723071098328
  std_inference_time_ms: 0.012980683046586046
model_complexity:
  macs: 1.123G
  macs_raw: 1122599296.0
  parameters: 950.489K
  params_raw: 950489.0
overall_metrics:
  accuracy: 66.58509615384615
  kappa: 0.6519280849358975
  macro_f1: 65.91652696975811
test_info:
  config_path: config.yaml
  model_path: ./saved_models/cldnn/torchsig1024_20250619_045536/models/best_model.pth
  test_date: '2025-06-21 21:58:13'
