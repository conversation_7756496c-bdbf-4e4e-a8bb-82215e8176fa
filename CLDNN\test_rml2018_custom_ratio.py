#!/usr/bin/env python3
"""
CLDNN RML2018数据集自定义测试集比例测试脚本
这个脚本专门用于测试RML2018数据集，支持从训练集中按SNR均衡地转移数据到测试集。
使用方法：python test_rml2018_custom_ratio.py --config config.yaml --model_path saved_models/best_model.pth
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import argparse
import numpy as np
import random
from tqdm import tqdm
import logging
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
import time
import json
from sklearn.metrics import confusion_matrix, classification_report, f1_score, cohen_kappa_score

# 尝试导入计算MACs
try:
    from thop import profile, clever_format
    THOP_AVAILABLE = True
except ImportError:
    try:
        from ptflops import get_model_complexity_info
        PTFLOPS_AVAILABLE = True
        THOP_AVAILABLE = False
    except ImportError:
        THOP_AVAILABLE = False
        PTFLOPS_AVAILABLE = False
        print("警告: 未安装thop或ptflops库，无计算MACs请运行: pip install thop 或 pip install ptflops")

# 导入模型和工具
from models import CLDNN
from utils.dataset import (
    load_rml_dataset, split_dataset, RML2016Dataset,
    get_hisar_data_loaders, get_torchsig_data_loaders, get_rml201801a_data_loaders
)

# ==================== 可调节参数 ====================
# 从训练集取样的比例（例如：0.1表示从训练集中取10%的数据
TRAIN_SAMPLE_RATIO = 0.2  # 可以调整这个值来控制从训练集取样的比例

# 从验证集取样的比例（例如：1.0表示使用全部验证集数据）
VAL_SAMPLE_RATIO = 0   # 可以调整这个值来控制从验证集取样的比例

# 从测试集取样的比例（例如：1.0表示使用全部测试集数据）
TEST_SAMPLE_RATIO = 0.6   # 可以调整这个值来控制从测试集取样的比例
# ===================================================

def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    log_file = os.path.join(output_dir, f'test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_custom_test_set_balanced(X_train, y_train, snr_train, X_val, y_val, snr_val,
                                   X_test, y_test, snr_test,
                                   train_sample_ratio, val_sample_ratio, test_sample_ratio, seed=42):
    """
    从训练集、验证集和测试集中按指定比例取样，生成新的测试集，保持SNR和调制类型双重均衡

    参数:
        X_train, y_train, snr_train: 训练集数据
        X_val, y_val, snr_val: 验证集数据
        X_test, y_test, snr_test: 原始测试集数据
        train_sample_ratio: 从训练集取样的比例
        val_sample_ratio: 从验证集取样的比例
        test_sample_ratio: 从测试集取样的比例
        seed: 随机种子

    返回:
        新的测试集数据 (X_new_test, y_new_test, snr_new_test)
    """
    np.random.seed(seed)

    print(f"\n开始创建自定义测试集...")
    print(f"从训练集取样比例: {train_sample_ratio*100:.1f}%")
    print(f"从验证集取样比例: {val_sample_ratio*100:.1f}%")
    print(f"从测试集取样比例: {test_sample_ratio*100:.1f}%")
    print(f"原始训练集大小: {len(X_train)}")
    print(f"原始验证集大小: {len(X_val)}")
    print(f"原始测试集大小: {len(X_test)}")

    # 获取唯一的SNR和标签值（合并训练集、验证集和测试集的信息）
    all_snrs = np.concatenate([snr_train, snr_val, snr_test])
    all_labels = np.concatenate([y_train, y_val, y_test])
    unique_snrs = np.unique(all_snrs)
    unique_labels = np.unique(all_labels)

    print(f"数据集统计信息:")
    print(f"  SNR级别数量: {len(unique_snrs)}")
    print(f"  调制类型数量: {len(unique_labels)}")
    print(f"  总组合数: {len(unique_snrs) * len(unique_labels)}")

    train_sample_indices = []
    val_sample_indices = []
    test_sample_indices = []

    # 对每个SNR和标签组合进行均衡取样
    total_combinations = 0
    successful_train_samples = 0
    successful_val_samples = 0
    successful_test_samples = 0

    for snr_value in unique_snrs:
        snr_train_sample_count = 0
        snr_val_sample_count = 0
        snr_test_sample_count = 0

        for label_val in unique_labels:
            total_combinations += 1

            # 从训练集中取样
            train_mask = (snr_train == snr_value) & (y_train == label_val)
            train_indices = np.where(train_mask)[0]

            if len(train_indices) > 0 and train_sample_ratio > 0:
                # 随机打乱
                np.random.shuffle(train_indices)

                # 计算要取样的数量
                n_train_sample = int(len(train_indices) * train_sample_ratio)
                if n_train_sample == 0 and len(train_indices) > 0 and train_sample_ratio > 0:
                    n_train_sample = 1  # 至少取一个样本

                if n_train_sample > 0:
                    train_sample_indices.extend(train_indices[:n_train_sample])
                    successful_train_samples += 1
                    snr_train_sample_count += n_train_sample
                    print(f"  训练集 SNR {snr_value:2.0f}dB, 类型ID {label_val}: 取样 {n_train_sample}/{len(train_indices)} 样本")

            # 从验证集中取样
            val_mask = (snr_val == snr_value) & (y_val == label_val)
            val_indices = np.where(val_mask)[0]

            if len(val_indices) > 0 and val_sample_ratio > 0:
                # 随机打乱
                np.random.shuffle(val_indices)

                # 计算要取样的数量
                n_val_sample = int(len(val_indices) * val_sample_ratio)
                if n_val_sample == 0 and len(val_indices) > 0 and val_sample_ratio > 0:
                    n_val_sample = 1  # 至少取一个样本

                if n_val_sample > 0:
                    val_sample_indices.extend(val_indices[:n_val_sample])
                    successful_val_samples += 1
                    snr_val_sample_count += n_val_sample
                    print(f"  验证集 SNR {snr_value:2.0f}dB, 类型ID {label_val}: 取样 {n_val_sample}/{len(val_indices)} 样本")

            # 从测试集中取样
            test_mask = (snr_test == snr_value) & (y_test == label_val)
            test_indices = np.where(test_mask)[0]

            if len(test_indices) > 0 and test_sample_ratio > 0:
                # 随机打乱
                np.random.shuffle(test_indices)

                # 计算要取样的数量
                n_test_sample = int(len(test_indices) * test_sample_ratio)
                if n_test_sample == 0 and len(test_indices) > 0 and test_sample_ratio > 0:
                    n_test_sample = 1  # 至少取一个样本

                if n_test_sample > 0:
                    test_sample_indices.extend(test_indices[:n_test_sample])
                    successful_test_samples += 1
                    snr_test_sample_count += n_test_sample
                    print(f"  测试集 SNR {snr_value:2.0f}dB, 类型ID {label_val}: 取样 {n_test_sample}/{len(test_indices)} 样本")

    # 从训练集获取取样的数据
    X_train_samples = X_train[train_sample_indices] if len(train_sample_indices) > 0 else np.array([]).reshape(0, *X_train.shape[1:])
    y_train_samples = y_train[train_sample_indices] if len(train_sample_indices) > 0 else np.array([])
    snr_train_samples = snr_train[train_sample_indices] if len(train_sample_indices) > 0 else np.array([])

    # 从验证集获取取样的数据
    X_val_samples = X_val[val_sample_indices] if len(val_sample_indices) > 0 else np.array([]).reshape(0, *X_val.shape[1:])
    y_val_samples = y_val[val_sample_indices] if len(val_sample_indices) > 0 else np.array([])
    snr_val_samples = snr_val[val_sample_indices] if len(val_sample_indices) > 0 else np.array([])

    # 从测试集获取取样的数据
    X_test_samples = X_test[test_sample_indices] if len(test_sample_indices) > 0 else np.array([]).reshape(0, *X_test.shape[1:])
    y_test_samples = y_test[test_sample_indices] if len(test_sample_indices) > 0 else np.array([])
    snr_test_samples = snr_test[test_sample_indices] if len(test_sample_indices) > 0 else np.array([])

    # 合并创建新的测试集
    all_samples = []
    if len(train_sample_indices) > 0:
        all_samples.append((X_train_samples, y_train_samples, snr_train_samples))
    if len(val_sample_indices) > 0:
        all_samples.append((X_val_samples, y_val_samples, snr_val_samples))
    if len(test_sample_indices) > 0:
        all_samples.append((X_test_samples, y_test_samples, snr_test_samples))

    if len(all_samples) == 0:
        raise ValueError("没有取样到任何数据，请检查取样比例设置")
    elif len(all_samples) == 1:
        X_new_test, y_new_test, snr_new_test = all_samples[0]
    else:
        # 合并多个数据源
        X_list, y_list, snr_list = zip(*all_samples)
        X_new_test = np.concatenate(X_list, axis=0)
        y_new_test = np.concatenate(y_list, axis=0)
        snr_new_test = np.concatenate(snr_list, axis=0)

    print(f"\n自定义测试集创建完成:")
    print(f"  从训练集取样: {len(train_sample_indices)} 样本")
    print(f"  从验证集取样: {len(val_sample_indices)} 样本")
    print(f"  从测试集取样: {len(test_sample_indices)} 样本")
    print(f"  新测试集总大小: {len(X_new_test)} 样本")
    print(f"  训练集实际取样比例: {len(train_sample_indices)/len(X_train)*100:.2f}%")
    print(f"  验证集实际取样比例: {len(val_sample_indices)/len(X_val)*100:.2f}%")
    print(f"  测试集实际取样比例: {len(test_sample_indices)/len(X_test)*100:.2f}%")
    print(f"  成功取样的训练集组合数: {successful_train_samples}/{total_combinations}")
    print(f"  成功取样的验证集组合数: {successful_val_samples}/{total_combinations}")
    print(f"  成功取样的测试集组合数: {successful_test_samples}/{total_combinations}")

    # 验证SNR分布（不泄露调制类型信息）
    print(f"\n新测试集SNR分布验证:")
    unique_new_snrs, new_counts = np.unique(snr_new_test, return_counts=True)
    for snr, count in zip(unique_new_snrs, new_counts):
        print(f"  SNR {snr:2.0f}dB: {count} 样本")

    # 验证调制类型分布的均衡性（不显示具体类型名称）
    print(f"\n调制类型分布均衡性验证:")
    unique_new_labels, new_label_counts = np.unique(y_new_test, return_counts=True)
    for i, (label, count) in enumerate(zip(unique_new_labels, new_label_counts)):
        print(f"  类型ID {label}: {count} 样本")

    # 检查分布的均衡性
    if len(new_label_counts) > 1:
        label_count_std = np.std(new_label_counts)
        label_count_mean = np.mean(new_label_counts)
        cv = label_count_std / label_count_mean * 100  # 变异系数
        print(f"  类型分布变异系数: {cv:.2f}% (越小越均衡)")
    else:
        print(f"  只有一种调制类型，无需计算变异系数")

    return X_new_test, y_new_test, snr_new_test

def get_rml201801a_data_loaders_custom(config, train_sample_ratio, val_sample_ratio, test_sample_ratio):
    """获取RML2018.01a数据集的自定义数据加载器"""
    # 获取数据集特定的batch_size（如果有的话）
    dataset_type = config['data']['dataset_type']
    if 'dataset_specific_params' in config['model']:
        dataset_params = config['model']['dataset_specific_params'].get(dataset_type, {})
        batch_size = dataset_params.get('batch_size', config['training']['batch_size'])
    else:
        batch_size = config['training']['batch_size']

    # 获取数据文件路径
    data_file = config['data'].get('rml201801a_file_path', 'data/GOLD_XYZ_OSC.0001_1024.hdf5')

    # 获取调制类型配置
    modulations = config['data'].get('rml201801a_modulations', None)  # None表示使用所有调制类型
    use_all_snr = config['data'].get('rml201801a_use_all_snr', True)  # 是否使用所有SNR级别

    # 获取数据集划分比例
    train_ratio = config['data'].get('train_ratio', 0.7)
    test_ratio = config['data'].get('test_ratio', 0.15)
    val_ratio = config['data'].get('val_ratio', 0.15)

    print(f"RML2018.01a数据集配置:")
    print(f"  数据文件: {data_file}")
    print(f"  调制类型: {'全部' if modulations is None else modulations}")
    print(f"  使用全部SNR: {use_all_snr}")
    print(f"  原始划分比例 - 训练: {train_ratio}, 测试: {test_ratio}, 验证: {val_ratio}")
    print(f"  训练集取样比例: {train_sample_ratio*100:.1f}%")
    print(f"  验证集取样比例: {val_sample_ratio*100:.1f}%")
    print(f"  测试集取样比例: {test_sample_ratio*100:.1f}%")

    # 加载数据集（使用原始划分）
    from utils.dataset import load_rml201801a_dataset
    (X_train, y_train, snr_train), (X_test, y_test, snr_test), (X_val, y_val, snr_val) = load_rml201801a_dataset(
        file_path=data_file,
        modulations=modulations,
        use_all_snr=use_all_snr,
        train_test_val_split=(train_ratio, test_ratio, val_ratio)
    )

    # 执行自定义的测试集创建
    X_test_new, y_test_new, snr_test_new = create_custom_test_set_balanced(
        X_train, y_train, snr_train, X_val, y_val, snr_val, X_test, y_test, snr_test,
        train_sample_ratio=train_sample_ratio,
        val_sample_ratio=val_sample_ratio,
        test_sample_ratio=test_sample_ratio,
        seed=config['training']['seed']
    )

    # 创建数据集
    from utils.dataset import RML201801aDataset
    test_dataset = RML201801aDataset(X_test_new, y_test_new, snr_test_new)

    # 创建数据加载器
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    return None, None, test_loader

def get_data_loaders(config, train_sample_ratio, val_sample_ratio, test_sample_ratio):
    """根据配置获取数据加载器"""
    dataset_type = config['data']['dataset_type']

    if dataset_type == 'rml201801a':
        return get_rml201801a_data_loaders_custom(config, train_sample_ratio, val_sample_ratio, test_sample_ratio)
    else:
        raise ValueError(f"此脚本仅支持RML2018.01a数据集，当前数据集类型: {dataset_type}")

def load_model(model_path, config, device):
    """加载训练好的模型"""
    checkpoint = torch.load(model_path, map_location=device)

    # 从检查点获取配置（如果有的话）
    if 'config' in checkpoint:
        saved_config = checkpoint['config']
        # 使用保存的模型配置
        model_config = saved_config['model']
        dataset_type = saved_config['data']['dataset_type']
    else:
        # 使用当前配置
        model_config = config['model']
        dataset_type = config['data']['dataset_type']

    # 获取数据集特定的模型参数
    if 'dataset_specific_params' in model_config and dataset_type in model_config['dataset_specific_params']:
        dataset_params = model_config['dataset_specific_params'].get(dataset_type, {})
        dropout_rate = dataset_params.get('dropout_rate', model_config.get('dropout_rate', 0.5))
        conv_channels = dataset_params.get('conv_channels', model_config.get('conv_channels', 50))
        lstm_units = dataset_params.get('lstm_units', model_config.get('lstm_units', 50))
        fc_units = dataset_params.get('fc_units', model_config.get('fc_units', 256))
        conv_kernel_size = dataset_params.get('conv_kernel_size', model_config.get('conv_kernel_size', 8))
    else:
        # 兼容旧版本配置或没有数据集特定参数的情况
        dropout_rate = model_config.get('dropout_rate', 0.5)
        conv_channels = model_config.get('conv_channels', 50)
        lstm_units = model_config.get('lstm_units', 50)
        fc_units = model_config.get('fc_units', 256)
        conv_kernel_size = model_config.get('conv_kernel_size', 8)

    # 根据数据集类型调整参数
    if dataset_type == 'rml':
        num_classes = len(config['rml_class_names'])
        sequence_length = config['data']['sequence_lengths']['rml']
    elif dataset_type == 'rml201801a':
        num_classes = len(config['rml201801a_class_names'])
        sequence_length = config['data']['sequence_lengths']['rml201801a']
    elif dataset_type == 'hisar':
        num_classes = len(config['hisar_class_names'])
        sequence_length = config['data']['sequence_lengths']['hisar']
    elif dataset_type.startswith('torchsig'):
        num_classes = len(config['torchsig_class_names'])
        sequence_length = config['data']['sequence_lengths'][dataset_type]
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

    print(f"加载模型 - 数据集: {dataset_type}, 序列长度: {sequence_length}")
    print(f"卷积通道: {conv_channels}, LSTM单元: {lstm_units}, 全连接单元: {fc_units}")
    print(f"Dropout率: {dropout_rate}, 卷积核大小: {conv_kernel_size}")

    # 创建模型
    model = CLDNN(
        in_channels=model_config['in_channels'],
        num_classes=num_classes,
        sequence_length=sequence_length,
        dropout_rate=dropout_rate,
        conv_channels=conv_channels,
        lstm_units=lstm_units,
        fc_units=fc_units,
        conv_kernel_size=conv_kernel_size
    )

    # 加载权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)

    return model

def calculate_model_macs(model, input_shape, device):
    """计算模型的MACs"""
    try:
        if THOP_AVAILABLE:
            # 使用thop库
            dummy_input = torch.randn(1, *input_shape).to(device)
            macs, params = profile(model, inputs=(dummy_input,), verbose=False)
            macs_str, params_str = clever_format([macs, params], "%.3f")
            return macs, macs_str, params, params_str
        elif PTFLOPS_AVAILABLE:
            # 使用ptflops库
            macs, params = get_model_complexity_info(
                model, input_shape,
                as_strings=True,
                print_per_layer_stat=False,
                verbose=False
            )
            return None, macs, None, params
        else:
            return None, "N/A (请安装thop或ptflops)", None, "N/A"
    except Exception as e:
        return None, f"计算失败: {str(e)}", None, "N/A"

def test_model(model, test_loader, device, logger):
    """测试模型"""
    model.eval()
    correct = 0
    total = 0
    all_predictions = []
    all_targets = []
    all_snrs = []
    inference_times = []

    with torch.no_grad():
        pbar = tqdm(test_loader, desc='Testing')
        for data, target, snr in pbar:
            data, target = data.to(device), target.to(device)

            # 测量推理时间
            start_time = time.time()
            output = model(data)
            end_time = time.time()

            # 记录每个样本的推理时间
            batch_inference_time = (end_time - start_time) / data.size(0)  # 每个样本的时间
            inference_times.extend([batch_inference_time] * data.size(0))

            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

            # 收集预测结果
            all_predictions.extend(pred.cpu().numpy().flatten())
            all_targets.extend(target.cpu().numpy())
            all_snrs.extend(snr.numpy())

            # 更新进度条
            pbar.set_postfix({'Acc': f'{100.*correct/total:.2f}%'})

    accuracy = 100. * correct / total

    # 计算额外的评估指标
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    all_snrs = np.array(all_snrs)
    inference_times = np.array(inference_times)

    macro_f1 = f1_score(all_targets, all_predictions, average='macro') * 100
    kappa = cohen_kappa_score(all_targets, all_predictions)

    # 推理时间统计
    avg_inference_time = np.mean(inference_times) * 1000  # 转换为毫秒
    std_inference_time = np.std(inference_times) * 1000

    logger.info(f'Test Accuracy: {accuracy:.2f}%')
    logger.info(f'Macro-F1: {macro_f1:.2f}%')
    logger.info(f'Kappa: {kappa:.4f}')
    logger.info(f'平均推理时间: {avg_inference_time:.3f} ± {std_inference_time:.3f} ms/sample')

    return accuracy, macro_f1, kappa, all_predictions, all_targets, all_snrs, inference_times

def analyze_results(predictions, targets, snrs, config, output_dir, logger, inference_times=None):
    """分析测试结果"""
    dataset_type = config['data']['dataset_type']

    # 获取类别名称
    if dataset_type == 'rml':
        class_names = config['rml_class_names']
    elif dataset_type == 'rml201801a':
        class_names = config['rml201801a_class_names']
    elif dataset_type == 'hisar':
        class_names = config['hisar_class_names']
    elif dataset_type.startswith('torchsig'):
        class_names = config['torchsig_class_names']
    else:
        class_names = [f'Class_{i}' for i in range(len(np.unique(targets)))]

    # 1. 总体混淆矩阵
    cm = confusion_matrix(targets, predictions)

    plt.figure(figsize=(12, 10))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Overall Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('Actual')
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'confusion_matrix_overall.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 保存混淆矩阵数据
    cm_data = {
        'confusion_matrix': cm.tolist(),
        'class_names': class_names
    }
    with open(os.path.join(output_dir, 'confusion_matrix_data.json'), 'w') as f:
        json.dump(cm_data, f, indent=2)

    # 2. 分类报告
    report = classification_report(targets, predictions, target_names=class_names, output_dict=True)

    # 保存分类报告
    with open(os.path.join(output_dir, 'classification_report.txt'), 'w') as f:
        f.write(classification_report(targets, predictions, target_names=class_names))

    # 3. 按SNR分析准确率和生成各SNR的混淆矩阵
    unique_snrs = np.unique(snrs)
    snr_accuracies = []
    snr_f1_scores = []
    snr_kappa_scores = []
    snr_results = {}

    # 创建SNR混淆矩阵目录
    snr_cm_dir = os.path.join(output_dir, 'snr_confusion_matrices')
    os.makedirs(snr_cm_dir, exist_ok=True)

    for snr in unique_snrs:
        mask = snrs == snr
        snr_targets = targets[mask]
        snr_predictions = predictions[mask]

        if len(snr_targets) > 0:
            snr_acc = np.mean(snr_targets == snr_predictions) * 100
            snr_f1 = f1_score(snr_targets, snr_predictions, average='macro') * 100
            snr_kappa = cohen_kappa_score(snr_targets, snr_predictions)

            snr_accuracies.append(snr_acc)
            snr_f1_scores.append(snr_f1)
            snr_kappa_scores.append(snr_kappa)

            # 保存SNR结果
            snr_results[f'SNR_{int(snr)}dB'] = {
                'accuracy': snr_acc,
                'macro_f1': snr_f1,
                'kappa': snr_kappa,
                'sample_count': len(snr_targets)
            }

            logger.info(f'SNR {snr:2.0f} dB: Acc={snr_acc:.2f}%, F1={snr_f1:.2f}%, Kappa={snr_kappa:.4f} (n={len(snr_targets)})')

            # 生成该SNR下的混淆矩阵
            snr_cm = confusion_matrix(snr_targets, snr_predictions)

            plt.figure(figsize=(10, 8))
            sns.heatmap(snr_cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=class_names, yticklabels=class_names)
            plt.title(f'Confusion Matrix - SNR {int(snr)} dB\nAcc: {snr_acc:.1f}%, F1: {snr_f1:.1f}%, Kappa: {snr_kappa:.3f}')
            plt.xlabel('Predicted')
            plt.ylabel('Actual')
            plt.xticks(rotation=45)
            plt.yticks(rotation=0)
            plt.tight_layout()
            plt.savefig(os.path.join(snr_cm_dir, f'confusion_matrix_snr_{int(snr)}dB.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

    # 保存SNR结果数据
    with open(os.path.join(output_dir, 'snr_results.json'), 'w') as f:
        json.dump(snr_results, f, indent=2)

    # 绘制SNR vs 各种指标曲线
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 准确率
    axes[0, 0].plot(unique_snrs, snr_accuracies, 'b-o', linewidth=2, markersize=6)
    axes[0, 0].set_xlabel('SNR (dB)')
    axes[0, 0].set_ylabel('Accuracy (%)')
    axes[0, 0].set_title('Accuracy vs SNR')
    axes[0, 0].grid(True, alpha=0.3)

    # Macro-F1
    axes[0, 1].plot(unique_snrs, snr_f1_scores, 'r-o', linewidth=2, markersize=6)
    axes[0, 1].set_xlabel('SNR (dB)')
    axes[0, 1].set_ylabel('Macro-F1 (%)')
    axes[0, 1].set_title('Macro-F1 vs SNR')
    axes[0, 1].grid(True, alpha=0.3)

    # Kappa
    axes[1, 0].plot(unique_snrs, snr_kappa_scores, 'g-o', linewidth=2, markersize=6)
    axes[1, 0].set_xlabel('SNR (dB)')
    axes[1, 0].set_ylabel('Kappa')
    axes[1, 0].set_title('Kappa vs SNR')
    axes[1, 0].grid(True, alpha=0.3)

    # 推理时间分布（如果有的话）
    if inference_times is not None:
        axes[1, 1].hist(inference_times * 1000, bins=50, alpha=0.7, color='purple')
        axes[1, 1].set_xlabel('Inference Time (ms)')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].set_title('Inference Time Distribution')
        axes[1, 1].grid(True, alpha=0.3)
    else:
        axes[1, 1].text(0.5, 0.5, 'No inference time data',
                       ha='center', va='center', transform=axes[1, 1].transAxes)
        axes[1, 1].set_title('Inference Time Distribution')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'performance_metrics_vs_snr.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 4. 每个类别的准确率
    class_accuracies = []
    for i, class_name in enumerate(class_names):
        mask = targets == i
        if np.sum(mask) > 0:
            class_acc = np.mean(predictions[mask] == targets[mask]) * 100
            class_accuracies.append(class_acc)
            logger.info(f'{class_name}: {class_acc:.2f}%')
        else:
            class_accuracies.append(0)

    # 绘制每个类别的准确率
    plt.figure(figsize=(12, 6))
    bars = plt.bar(range(len(class_names)), class_accuracies)
    plt.xlabel('Modulation Type')
    plt.ylabel('Accuracy (%)')
    plt.title('Per-Class Accuracy')
    plt.xticks(range(len(class_names)), class_names, rotation=45)

    # 添加数值标签
    for bar, acc in zip(bars, class_accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{acc:.1f}%', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'per_class_accuracy.png'), dpi=300, bbox_inches='tight')
    plt.close()

    return report

def find_latest_experiment_dir(base_dir, dataset_type):
    """查找最新的实验目录"""
    if not os.path.exists(base_dir):
        return None

    # 查找匹配的实验目录
    experiment_dirs = []
    for item in os.listdir(base_dir):
        if item.startswith(f"{dataset_type}_") and os.path.isdir(os.path.join(base_dir, item)):
            experiment_dirs.append(item)

    if not experiment_dirs:
        return None

    # 按时间戳排序，返回最新的
    experiment_dirs.sort(reverse=True)
    return os.path.join(base_dir, experiment_dirs[0])

def create_test_output_directories(config, model_path, train_sample_ratio, val_sample_ratio, test_sample_ratio):
    """创建测试输出目录结构"""
    dataset_type = config['data']['dataset_type']
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    sample_str = f"train{int(train_sample_ratio*100)}pct_val{int(val_sample_ratio*100)}pct_test{int(test_sample_ratio*100)}pct"

    # 基础输出目录
    base_output_dir = config['output_dir']

    # 创建测试结果目录
    test_dir = os.path.join(base_output_dir, f"test_{dataset_type}_{sample_str}_{timestamp}")

    # 创建子目录
    directories = {
        'test': test_dir,
        'results': os.path.join(test_dir, 'results'),
        'plots': os.path.join(test_dir, 'plots'),
        'logs': os.path.join(test_dir, 'logs'),
        'configs': os.path.join(test_dir, 'configs')
    }

    # 创建所有目录
    for dir_path in directories.values():
        os.makedirs(dir_path, exist_ok=True)

    print(f"测试结果目录创建完成: {test_dir}")
    print(f"子目录包括: results, plots, logs, configs")

    return directories

def main():
    parser = argparse.ArgumentParser(description='CLDNN RML2018自定义测试集比例测试脚本')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    parser.add_argument('--model_path', type=str, default=None, help='模型文件路径')
    parser.add_argument('--output_dir', type=str, default=None, help='结果输出目录')
    parser.add_argument('--auto_find', action='store_true', help='自动查找最新的训练模型')
    parser.add_argument('--train_sample_ratio', type=float, default=TRAIN_SAMPLE_RATIO,
                       help=f'从训练集取样的比例 (默认: {TRAIN_SAMPLE_RATIO})')
    parser.add_argument('--val_sample_ratio', type=float, default=VAL_SAMPLE_RATIO,
                       help=f'从验证集取样的比例 (默认: {VAL_SAMPLE_RATIO})')
    parser.add_argument('--test_sample_ratio', type=float, default=TEST_SAMPLE_RATIO,
                       help=f'从测试集取样的比例 (默认: {TEST_SAMPLE_RATIO})')
    args = parser.parse_args()

    # 加载配置
    config = load_config(args.config)
    dataset_type = config['data']['dataset_type']

    # 检查数据集类型
    if dataset_type != 'rml201801a':
        print(f"错误: 此脚本仅支持RML2018.01a数据集，当前配置的数据集类型为: {dataset_type}")
        print("请修改config.yaml中的dataset_type为'rml201801a'")
        return

    print(f"RML2018自定义测试集比例测试脚本")
    print(f"训练集取样比例: {args.train_sample_ratio*100:.1f}%")
    print(f"验证集取样比例: {args.val_sample_ratio*100:.1f}%")
    print(f"测试集取样比例: {args.test_sample_ratio*100:.1f}%")
    print(f"数据集类型: {dataset_type}")

    # 自动查找模型路径
    if args.model_path is None or args.auto_find:
        base_dir = config['output_dir']
        latest_exp_dir = find_latest_experiment_dir(base_dir, dataset_type)

        if latest_exp_dir:
            auto_model_path = os.path.join(latest_exp_dir, 'models', 'best_model.pth')
            if os.path.exists(auto_model_path):
                args.model_path = auto_model_path
                print(f"自动找到模型: {auto_model_path}")
            else:
                print(f"警告: 在 {latest_exp_dir} 中未找到 best_model.pth")

        if args.model_path is None:
            # 使用默认路径
            args.model_path = 'saved_models/cldnn/best_model.pth'

    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        print(f"错误: 模型文件不存在: {args.model_path}")
        print("请先训练模型或指定正确的模型路径:")
        print("  python test_rml2018_custom_ratio.py --model_path your_model_path.pth")
        print("或使用 --auto_find 参数自动查找最新模型:")
        print("  python test_rml2018_custom_ratio.py --auto_find")
        return

    # 创建测试输出目录结构
    if args.output_dir is None:
        directories = create_test_output_directories(config, args.model_path, args.train_sample_ratio, args.val_sample_ratio, args.test_sample_ratio)
        output_dir = directories['results']
    else:
        output_dir = args.output_dir
        os.makedirs(output_dir, exist_ok=True)
        directories = {'results': output_dir, 'logs': output_dir, 'configs': output_dir, 'plots': output_dir}

    # 设置日志（使用logs目录）
    logger = setup_logging(directories['logs'])
    logger.info(f"开始RML2018自定义测试集比例测试，配置文件: {args.config}")
    logger.info(f"模型文件: {args.model_path}")
    logger.info(f"数据集类型: {config['data']['dataset_type']}")
    logger.info(f"训练集取样比例: {args.train_sample_ratio*100:.1f}%")
    logger.info(f"验证集取样比例: {args.val_sample_ratio*100:.1f}%")
    logger.info(f"测试集取样比例: {args.test_sample_ratio*100:.1f}%")
    logger.info(f"测试结果目录: {directories.get('test', output_dir)}")

    # 保存配置文件副本到configs目录
    config_backup_path = os.path.join(directories['configs'], 'test_config_backup.yaml')
    with open(config_backup_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    logger.info(f"配置文件备份保存到: {config_backup_path}")

    # 设置设备
    device = torch.device(config['training']['device'] if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 设置随机种子
    torch.manual_seed(config['training']['seed'])
    np.random.seed(config['training']['seed'])
    random.seed(config['training']['seed'])
    logger.info(f"设置随机种子: {config['training']['seed']}")

    # 获取测试数据加载器
    logger.info("加载测试数据...")
    _, _, test_loader = get_data_loaders(config, args.train_sample_ratio, args.val_sample_ratio, args.test_sample_ratio)

    # 加载模型
    logger.info("加载模型...")
    model = load_model(args.model_path, config, device)

    # 计算模型MACs
    logger.info("计算模型复杂度...")
    dataset_type = config['data']['dataset_type']
    if dataset_type == 'rml':
        input_shape = (2, config['data']['sequence_lengths']['rml'])
    elif dataset_type == 'rml201801a':
        input_shape = (2, config['data']['sequence_lengths']['rml201801a'])
    elif dataset_type == 'hisar':
        input_shape = (2, config['data']['sequence_lengths']['hisar'])
    elif dataset_type.startswith('torchsig'):
        input_shape = (2, config['data']['sequence_lengths'][dataset_type])
    else:
        input_shape = (2, 1024)  # 默认值

    macs_raw, macs_str, params_raw, params_str = calculate_model_macs(model, input_shape, device)
    logger.info(f"模型MACs: {macs_str}")
    logger.info(f"模型参数: {params_str}")

    # 测试模型
    logger.info("开始测试...")
    accuracy, macro_f1, kappa, predictions, targets, snrs, inference_times = test_model(model, test_loader, device, logger)

    # 分析结果（使用plots目录保存图片，results目录保存数据）
    logger.info("分析结果...")
    report = analyze_results(predictions, targets, snrs, config, directories['plots'], logger, inference_times)

    # 保存详细的结果摘要
    summary = {
        'test_configuration': {
            'train_sample_ratio': args.train_sample_ratio,
            'train_sample_percentage': f"{args.train_sample_ratio*100:.1f}%",
            'val_sample_ratio': args.val_sample_ratio,
            'val_sample_percentage': f"{args.val_sample_ratio*100:.1f}%",
            'test_sample_ratio': args.test_sample_ratio,
            'test_sample_percentage': f"{args.test_sample_ratio*100:.1f}%",
            'original_train_ratio': config['data'].get('train_ratio', 0.7),
            'original_val_ratio': config['data'].get('val_ratio', 0.15),
            'original_test_ratio': config['data'].get('test_ratio', 0.15)
        },
        'overall_metrics': {
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'kappa': kappa
        },
        'model_complexity': {
            'macs': macs_str,
            'parameters': params_str,
            'macs_raw': macs_raw if macs_raw is not None else 'N/A',
            'params_raw': params_raw if params_raw is not None else 'N/A'
        },
        'inference_performance': {
            'avg_inference_time_ms': float(np.mean(inference_times) * 1000) if inference_times is not None else 'N/A',
            'std_inference_time_ms': float(np.std(inference_times) * 1000) if inference_times is not None else 'N/A',
            'min_inference_time_ms': float(np.min(inference_times) * 1000) if inference_times is not None else 'N/A',
            'max_inference_time_ms': float(np.max(inference_times) * 1000) if inference_times is not None else 'N/A'
        },
        'dataset_info': {
            'total_samples': len(targets),
            'dataset_type': config['data']['dataset_type'],
            'input_shape': input_shape,
            'num_classes': len(np.unique(targets)),
            'snr_range': [float(np.min(snrs)), float(np.max(snrs))]
        },
        'test_info': {
            'model_path': args.model_path,
            'config_path': args.config,
            'test_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'script_name': 'test_rml2018_custom_ratio.py'
        }
    }

    # 保存为YAML和JSON格式到results目录
    with open(os.path.join(directories['results'], 'test_summary.yaml'), 'w') as f:
        yaml.dump(summary, f, default_flow_style=False)

    with open(os.path.join(directories['results'], 'test_summary.json'), 'w') as f:
        json.dump(summary, f, indent=2)

    # 保存推理时间数据
    if inference_times is not None:
        inference_data = {
            'inference_times_ms': (inference_times * 1000).tolist(),
            'statistics': {
                'mean': float(np.mean(inference_times) * 1000),
                'std': float(np.std(inference_times) * 1000),
                'min': float(np.min(inference_times) * 1000),
                'max': float(np.max(inference_times) * 1000),
                'median': float(np.median(inference_times) * 1000)
            }
        }
        with open(os.path.join(directories['results'], 'inference_times.json'), 'w') as f:
            json.dump(inference_data, f, indent=2)

    logger.info(f"测试完成！")
    logger.info(f"训练集取样比例: {args.train_sample_ratio*100:.1f}%")
    logger.info(f"验证集取样比例: {args.val_sample_ratio*100:.1f}%")
    logger.info(f"测试集取样比例: {args.test_sample_ratio*100:.1f}%")
    logger.info(f"总体准确率: {accuracy:.2f}%")
    logger.info(f"Macro-F1: {macro_f1:.2f}%")
    logger.info(f"Kappa: {kappa:.4f}")
    logger.info(f"模型MACs: {macs_str}")
    if inference_times is not None:
        logger.info(f"平均推理时间: {np.mean(inference_times)*1000:.3f} ms/sample")
    logger.info(f"测试结果保存在: {directories.get('test', output_dir)}")
    logger.info(f"生成的文件包括:")
    logger.info(f"  - 图片文件: {directories['plots']}/")
    logger.info(f"    * 总体混淆矩阵: confusion_matrix_overall.png")
    logger.info(f"    * 各SNR混淆矩阵: snr_confusion_matrices/")
    logger.info(f"    * 性能指标图: performance_metrics_vs_snr.png")
    logger.info(f"  - 数据文件: {directories['results']}/")
    logger.info(f"    * 详细结果: test_summary.json, snr_results.json")
    logger.info(f"    * 推理时间: inference_times.json")
    logger.info(f"  - 日志文件: {directories['logs']}/")
    logger.info(f"  - 配置备份: {directories['configs']}/")

if __name__ == '__main__':
    main()
