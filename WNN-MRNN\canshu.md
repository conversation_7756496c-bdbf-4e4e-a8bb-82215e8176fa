# WNN-MRNN网络参数传递流图与数据维度 (num_levels=2)

## 引言

WNN-MRNN (小波神经网络-Mamba递归神经网络) 是一种用于调制识别的深度学习模型，它结合了小波分解和基于Mamba的递归神经网络。本文档详细描述了该网络的参数传递流程、数据维度变化以及各组件间的交互。通过分解信号到不同频率域并使用Mamba状态空间模型处理这些分量，模型能够有效捕获调制信号的时频特征。

## 1. 参数传递总览图与数据维度 (以num_levels=2为例)

```
WNN_MRNN
├── 输入参数:
│   ├── in_channels=2         # 输入通道数（I/Q信号）
│   ├── num_classes=11        # 分类类别数（调制方式的数量）
│   ├── wavelet_dim=96        # 小波分解输出通道数（特征维度）
│   ├── rnn_dim=160           # RNN隐藏层维度，同时也是Mamba特征维度
│   ├── num_layers=3          # MMRNNCell的层数（堆叠深度）
│   ├── msb_depth=2           # 每个MMRNNCell中MSB块的数量（控制复杂度）
│   ├── num_levels=2          # 小波分解层数，决定了高频分量的数量
│   │                         # 当num_levels=2时，产生2个高频分量+1个低频分量=3个总分量
│   ├── d_state=16            # Mamba状态空间模型的状态空间维度
│   └── drop_rate=0.159       # Dropout比率（防止过拟合）
│
├── 输入数据维度: [B, 2, 128]  # 批次大小B, 2通道(I/Q), 序列长度128
│                            # 2对应in_channels参数，128为固定序列长度
│
├── 初始卷积层处理:
│   ├── Conv1: [B, 2, 128] -> [B, 64, 128]   # 2D卷积(kernel_size=1)将通道从2扩展到64
│   ├── Conv2: [B, 64, 128] -> [B, 64, 128]  # 1D卷积(kernel_size=3)保持维度不变，提取局部特征
│   └── Conv3: [B, 64, 128] -> [B, 64, 128]  # 1D卷积(kernel_size=3)保持维度不变，进一步提取特征
│
├── 小波分解:             # 分解层数为num_levels=2，产生2个高频分量和1个低频分量
│   ├── 高频分量1: [B, 64, 64]   # 第一级高频分量，序列长度变为原长度的1/2 (128→64)
│   ├── 高频分量2: [B, 64, 32]   # 第二级高频分量，序列长度变为原长度的1/4 (128→32)
│   └── 低频分量: [B, 64, 32]    # 低频分量，序列长度与最后一级高频分量相同 (32)
│
├── 特征提取:             # 通道数由wavelet_dim=96决定
│   ├── 高频分量1: [B, 96, 64]   # 通道数从64变为wavelet_dim=96
│   ├── 高频分量2: [B, 96, 32]   # 通道数从64变为wavelet_dim=96
│   └── 低频分量: [B, 96, 32]    # 通道数从64变为wavelet_dim=96
│
├── 维度调整:             # 所有分量长度调整为与第一个高频分量相同
│   ├── 高频分量1: [B, 96, 64]   # 保持原始维度 (参考长度)
│   ├── 高频分量2: [B, 96, 32] → [B, 96, 64]   # 通过插值调整为参考长度
│   └── 低频分量: [B, 96, 32] → [B, 96, 64]    # 通过插值调整为参考长度
│
├── 维度重排:             # 交换通道维度和序列维度，为MRNN处理准备
│   ├── 高频分量1: [B, 96, 64] → [B, 64, 96]   # 转置，96是特征维度(wavelet_dim)
│   ├── 高频分量2: [B, 96, 64] → [B, 64, 96]   # 转置，96是特征维度(wavelet_dim)
│   └── 低频分量: [B, 96, 64] → [B, 64, 96]    # 转置，96是特征维度(wavelet_dim)
│
├── 分量组合: [B, 3, 64, 96]   # 按分量顺序堆叠所有分量
│                              # 3是分量数量，等于num_levels+1=2+1=3
│
├── 维度调整: [B, 3*64, 96]    # 重塑维度以适应MMRNNClassifier
│                              # 3*64=192是总序列长度，其中3由num_levels+1=3决定
│
├─→ MMRNNClassifier
│   ├── 接收参数:
│   │   ├── input_dim=wavelet_dim=96    # 从WNN_MRNN接收，决定输入特征维度
│   │   ├── hidden_dim=rnn_dim=160      # 从WNN_MRNN接收，决定隐藏状态维度
│   │   ├── num_components=num_levels+1=3 # 从WNN_MRNN接收，决定分量数量(2+1=3)
│   │   ├── num_layers=num_layers=3     # 从WNN_MRNN接收，决定MRNN层数
│   │   ├── msb_depth=msb_depth=2       # 从WNN_MRNN接收，控制每个MMRNNCell中MSB块的数量
│   │   ├── num_classes=num_classes=11  # 从WNN_MRNN接收，决定输出类别数
│   │   ├── d_state=d_state=16          # 从WNN_MRNN接收，决定Mamba状态空间维度
│   │   ├── d_conv=4                    # 默认值，决定Mamba卷积核大小
│   │   ├── expand=2                    # 默认值，决定Mamba特征扩展倍数
│   │   └── dropout=drop_rate=0.159       # 从WNN_MRNN接收，决定dropout比率
│   │
│   ├── 输入投影: [B, 3*64, 96]           # 输入数据进行特征投影
│   │   └── 投影后: [B, 3*64, 160]         # 投影到hidden_dim=160维度
│   │
│   ├── 重塑为分量序列: [B, 3, 64, 160]     # 将输入重塑为分量和序列维度分离的形式
│   │                                     # 3由num_components=3决定(num_levels+1=3)
│   │
│   ├── 分量循环: [B, 3, 64, 160]          # 逐分量(c=0到c=2)处理序列，共处理3个分量
│   │   ├── 高频分量1(c=0): [B, 64, 160]   # 第一个处理的分量
│   │   ├── 高频分量2(c=1): [B, 64, 160]   # 第二个处理的分量
│   │   └── 低频分量(c=2): [B, 64, 160]    # 最后处理的分量
│   │
│   ├─→ MMRNNCell (第1层)
│   │   ├── 接收参数:
│   │   │   ├── hidden_dim=rnn_dim=160      # 从MMRNNClassifier接收，决定隐藏层维度
│   │   │   ├── msb_depth=msb_depth=2       # 从MMRNNClassifier接收，控制MSB块的数量
│   │   │   ├── drop_path=dropout=0.159       # 从MMRNNClassifier接收，控制丢弃率
│   │   │   ├── norm_layer=nn.LayerNorm     # 默认值，决定归一化层类型
│   │   │   ├── d_state=d_state=16          # 从MMRNNClassifier接收，控制状态空间维度
│   │   │   ├── d_conv=d_conv=4             # 从MMRNNClassifier接收，控制卷积核大小
│   │   │   └── expand=expand=2             # 从MMRNNClassifier接收，控制特征扩展倍数
│   │   │
│   │   ├── 输入数据: [B, 64, 160]          # 批次大小B, 序列长度64, 特征维度160
│   │   │                                  # 160由hidden_dim决定
│   │   ├─→ MSB (msb_depth=2个块)
│   │       ├── 接收参数:
│   │       │   ├── hidden_dim=rnn_dim=160      # 从MMRNNCell接收，决定隐藏层维度
│   │       │   ├── drop_path=drop_path=0.159     # 从MMRNNCell接收，控制丢弃率
│   │       │   ├── norm_layer=norm_layer       # 从MMRNNCell接收，决定归一化层类型
│   │       │   ├── d_state=d_state=16          # 从MMRNNCell接收，控制状态空间维度
│   │       │   ├── d_conv=d_conv=4             # 从MMRNNCell接收，控制卷积核大小
│   │       │   └── expand=expand=2             # 从MMRNNCell接收，控制特征扩展倍数
│   │       │
│   │       ├── 输入数据: [B, 64, 160]          # 批次大小B, 序列长度64, 特征维度160
│   │       │                                  # 160由hidden_dim决定
│   │       ├── Mamba直接处理:
│   │       │   ├── Mamba处理: [B, 64, 160] → [B, 64, 160]  # 直接处理，无需维度调整
│   │       │
│   │       └─→ MSSBlock (父类)
│   │           ├── 接收参数:
│   │           │   ├── hidden_dim=rnn_dim=160       # 从MSB接收，决定隐藏层维度
│   │           │   ├── drop_path=drop_path=0.159     # 从MSB接收，控制丢弃率
│   │           │   ├── norm_layer=norm_layer       # 从MSB接收，决定归一化层类型
│   │           │   ├── d_state=d_state=16          # 从MSB接收，控制状态空间维度
│   │           │   ├── d_conv=d_conv=4             # 从MSB接收，控制卷积核大小
│   │           │   └── expand=expand=2             # 从MSB接收，控制特征扩展倍数
│   │           │
│   │           ├── 输入数据: [B, 64, 160]            # 批次大小B, 序列长度64, 特征维度160
│   │           │                                    # 160由hidden_dim决定
│   │           └─→ Mamba
│   │               ├── 输入数据: [B, 64, 160]          # 批次大小B, 序列长度64, 特征维度160
│   │               │
│   │               ├── 参数设置:
│   │               │   ├── d_model=hidden_dim=160      # 内部模型维度，由hidden_dim决定
│   │               │   ├── d_state=16                  # 状态空间维度，由d_state决定
│   │               │   ├── d_conv=4                    # 卷积核大小，由d_conv决定
│   │               │   ├── expand=2                    # 特征扩展因子，由expand决定
│   │               │   └── d_inner=d_model*expand=160*2=320  # 内部扩展维度，由d_model和expand共同决定
│   │               │
│   │               ├── Mamba内部处理流程:
│   │               │   ├── 特征处理和SSM: [B, 64, 160] → [B, 64, 160]
│   │               │
│   │               └── 输出数据: [B, 64, 160]           # 处理后的特征，维度不变
│   │
│   ├── MMRNNCell第1层单个分量输出: [B, 64, 160]     # 第1层MMRNNCell的输出
│   │   └── 隐藏状态更新: (Ht, Ct)                  # 更新隐藏状态和单元状态，传递给下一分量
│   │
│   ├─→ MMRNNCell (第2层)
│   │   ├── 接收参数:
│   │   │   ├── hidden_dim=rnn_dim=160       # 从MMRNNClassifier接收，决定隐藏层维度
│   │   │   ├── msb_depth=msb_depth=2        # 从MMRNNClassifier接收，控制MSB块的数量
│   │   │   ├── drop_path=dropout=0.159       # 从MMRNNClassifier接收，控制丢弃率
│   │   │   ├── norm_layer=nn.LayerNorm     # 默认值，决定归一化层类型
│   │   │   ├── d_state=d_state=16          # 从MMRNNClassifier接收，控制状态空间维度
│   │   │   ├── d_conv=d_conv=4             # 从MMRNNClassifier接收，控制卷积核大小
│   │   │   └── expand=expand=2             # 从MMRNNClassifier接收，控制特征扩展倍数
│   │   │
│   │   ├── 输入数据: [B, 64, 160]          # 批次大小B, 序列长度64, 特征维度160
│   │   │                                  # (来自第1层MMRNNCell的当前分量输出)
│   │   │
│   │   ├─→ MSB (msb_depth=2个块)
│   │       ├── 参数与第1层相同
│   │       │
│   │       ├── 输入数据: [B, 64, 160]      # 批次大小B, 序列长度64, 特征维度160
│   │       │
│   │       ├── Mamba直接处理:
│   │       │   ├── 处理流程与第1层相同     # 直接通过Mamba处理
│   │   
│   ├── MMRNNCell第2层单个分量输出: [B, 64, 160]  # 第2层MMRNNCell在当前分量的输出
│   │   └── 隐藏状态更新: (Ht, Ct)              # 更新隐藏状态和单元状态，传递给下一分量
│   │
│   ├─→ MMRNNCell (第3层)
│   │   ├── 接收参数:
│   │   │   ├── hidden_dim=rnn_dim=160       # 从MMRNNClassifier接收，决定隐藏层维度
│   │   │   ├── msb_depth=msb_depth=2        # 从MMRNNClassifier接收，控制MSB块的数量
│   │   │   ├── drop_path=dropout=0.159       # 从MMRNNClassifier接收，控制丢弃率
│   │   │   ├── norm_layer=nn.LayerNorm     # 默认值，决定归一化层类型
│   │   │   ├── d_state=d_state=16          # 从MMRNNClassifier接收，控制状态空间维度
│   │   │   ├── d_conv=d_conv=4             # 从MMRNNClassifier接收，控制卷积核大小
│   │   │   └── expand=expand=2             # 从MMRNNClassifier接收，控制特征扩展倍数
│   │   │
│   │   ├── 输入数据: [B, 64, 160]          # 批次大小B, 序列长度64, 特征维度160
│   │   │                                  # (来自第2层MMRNNCell的当前分量输出)
│   │   │
│   │   ├─→ MSB (msb_depth=2个块)
│   │       ├── 参数与第1层相同
│   │       │
│   │       ├── 输入数据: [B, 64, 160]      # 批次大小B, 序列长度64, 特征维度160
│   │       │
│   │       ├── Mamba直接处理:
│   │       │   ├── 处理流程与第1层相同     # 直接通过Mamba处理
│   │   
│   ├── MMRNNCell第3层单个分量输出: [B, 64, 160]  # 第3层MMRNNCell在当前分量的输出
│   │   └── 隐藏状态更新: (Ht, Ct)              # 更新隐藏状态和单元状态，传递给下一分量
│   │
│   ├── 收集所有分量输出: [c=0,...,c=2]        # 存储每个分量的最终输出
│   │                                        # 共num_components=3个分量
│   ├── 取最后一个分量(低频): [B, 64, 160]      # 提取最后一个分量(低频)的特征
│   │
│   ├── 平均池化: [B, 64, 160] -> [B, 160]     # 在序列维度上进行平均池化
│   │                                        # 输出维度160由hidden_dim决定
│   ├── 分类头输出: [B, 11]                    # 通过线性层映射到11个类别
│   │                                        # 11由num_classes决定
│
└── 最终输出: [B, 11]                          # 网络输出, 每类的预测分数
                                            # 11由num_classes决定
```

## 2. 参数传递详解与数据流

### 2.1 输入数据流与初始处理

输入IQ信号的维度为[B, 2, 128]，其中B是批次大小，2表示I/Q两个通道，128是序列长度。通道数由`in_channels=2`参数决定。

```python
# 初始卷积层处理步骤
x = self._process_initial_features(x)  # [B, 2, 128] -> [B, 64, 128]
```

数据首先通过一系列卷积层处理：
1. 2D卷积 `conv1`: [B, 2, 128] -> [B, 64, 128]，使用1×1卷积核扩展通道数
   ```python
   # 从AWN模型添加的初始卷积层
   # 1. 2D卷积，处理IQ通道
   self.conv1 = nn.Conv2d(1, 64, kernel_size=(2, 7), padding=(0, 3))
   self.bn1 = nn.BatchNorm2d(64)
   ```
   此处的2D卷积使用kernel_size=(2,7)，特别设计用于处理IQ信号的两个通道，同时捕获时间域的局部特征。

2. 1D卷积 `conv2`: [B, 64, 128] -> [B, 64, 128]，使用5×1卷积核提取局部特征
   ```python
   # 2. 1D卷积序列
   self.conv2 = nn.Conv1d(64, 64, kernel_size=5, padding=2)
   self.bn2 = nn.BatchNorm1d(64)
   ```
   kernel_size=5的卷积提供了较大的感受野，可以捕获更宽范围的时间特征。

3. 1D卷积 `conv3`: [B, 64, 128] -> [B, 64, 128]，使用3×1卷积核进一步提取特征
   ```python
   self.conv3 = nn.Conv1d(64, 64, kernel_size=3, padding=1)
   self.bn3 = nn.BatchNorm1d(64)
   ```
   kernel_size=3的卷积进一步细化特征提取，关注更细粒度的局部模式。

每个卷积层后都应用了批归一化(BatchNorm)和ReLU激活函数，增强特征表达能力并提高训练稳定性。处理流程如下：
```python
def _process_initial_features(self, x):
    # 如有必要，添加通道维度: [B, 2, L] -> [B, 1, 2, L]
    if x.dim() == 3:
        x = x.unsqueeze(1)
        
    # 2D卷积处理IQ通道
    x = self.conv1(x)
    x = self.bn1(x)
    x = self.relu(x)
    
    # 去除多余维度: [B, 64, 1, L] -> [B, 64, L]
    x = x.squeeze(2)
    
    # 1D卷积序列
    x = self.conv2(x)
    x = self.bn2(x)
    x = self.relu(x)
    
    x = self.conv3(x)
    x = self.bn3(x)
    x = self.relu(x)
    
    return x
```

### 2.2 小波分解与特征提取

```python
# 小波分解与特征提取
high_features_list, low_features = self._process_input(x)  # [B, 64, 128] -> 多个分量
```

小波分解是一种多分辨率分析方法，可以将信号分解为不同频率范围的分量。在设置`num_levels=2`的情况下，执行二级小波分解，产生以下分量：

1. 高频分量1: [B, 64, 64] - 序列长度变为原长度的1/2 (128→64)
   - 捕获信号中的高频细节，如快速变化的特征
   - 使用小波高通滤波器提取
   - 包含调制信号中的瞬态特征和快速变化的相位信息

2. 高频分量2: [B, 64, 32] - 序列长度变为原长度的1/4 (128→32)
   - 捕获中高频特征，分辨率低于第一级高频分量
   - 通过对第一级低频分量再次应用高通滤波获得
   - 表示中等时间尺度的特征变化

3. 低频分量: [B, 64, 16] - 序列长度与最后一级高频分量相同 (16)
   - 捕获信号中的低频趋势，代表信号的近似部分
   - 通过对第二级低频分量应用低通滤波获得
   - 包含信号的整体特征和长期趋势

高频分量数量等于`num_levels=2`，总分量数量等于`num_levels+1=3`。

本项目中的小波分解使用了提升方案(Lifting Scheme)，它是一种快速计算小波变换的方法，通过预测和更新操作实现：

```python
class WaveletDecomposition(nn.Module):
    def __init__(self, channels, decomposition_levels=2):
        super(WaveletDecomposition, self).__init__()
        self.decomposition_levels = decomposition_levels
        
        # 创建预测算子模块列表，每个级别一个
        self.predictors = nn.ModuleList([
            PredictUpdateOperator(channels) for _ in range(decomposition_levels)
        ])
        
        # 创建更新算子模块列表，每个级别一个
        self.updaters = nn.ModuleList([
            PredictUpdateOperator(channels) for _ in range(decomposition_levels)
        ])
```

每个`PredictUpdateOperator`使用双向LSTM学习优化的小波滤波器，取代了传统固定滤波器的小波变换：

```python
class PredictUpdateOperator(nn.Module):
    def __init__(self, channels):
        super(PredictUpdateOperator, self).__init__()
        # 使用双向LSTM处理序列数据
        self.lstm = nn.LSTM(
            input_size=channels,
            hidden_size=channels,
            num_layers=1,
            batch_first=False,
            bidirectional=True
        )
        # 线性层将双向LSTM的输出映射回原始通道数
        self.linear = nn.Linear(channels * 2, channels)
        # Tanh激活函数用于限制输出范围
        self.tanh = nn.Tanh()
```

小波分解的前向传播过程按照以下步骤执行：
1. 分裂(Split)：将信号分为奇偶样本
2. 更新(Update)：使用奇数样本更新偶数样本，形成低频分量
3. 预测(Predict)：使用更新后的低频分量预测奇数样本，形成高频分量

对于每一级分解，序列长度减半，高频分量捕获细节，低频分量继续进行下一级分解：

```python
def forward(self, x, return_intermediate=False):
    # 多级分解
    for i in range(self.decomposition_levels):
        # 分裂：将当前特征分为奇偶两部分
        even, odd = self.split(feature_map)
        
        # 更新：使用奇数部分计算更新值，并与偶数部分相加
        # L = even + U(odd)
        low_freq = even + self.updaters[i](odd)
        
        # 预测：使用更新后的低频分量预测奇数部分，计算高频分量
        # H = odd - P(low_freq)
        high_freq = odd - self.predictors[i](low_freq)
        high_freqs.append(high_freq)
        
        # 更新特征图为当前的低频分量，进行下一级分解
        feature_map = low_freq
```

每个分量通过独立的卷积层处理，通道数从64变为`wavelet_dim=96`：

2. 状态更新机制:
   MMRNNCell使用类似LSTM的机制，但使用Mamba处理序列信息：
   ```python
   def forward(self, xt, hidden_states):
       # 省略前面的代码...
       
       # 通过MSB处理输入和状态
       for index, layer in enumerate(self.MSBs):
           if index == 0:
               x = layer(xt, hx)  # 第一层接收输入和隐藏状态
           else:
               x = layer(outputs[-1], None)  # 后续层仅使用前一层输出
       
       o_t = outputs[-1]  # 最后一层的输出
       
       # 计算门控系数，使用sigmoid激活函数
       Ft = torch.sigmoid(o_t)
       
       # 计算候选单元状态，使用tanh激活函数
       cell = torch.tanh(o_t)
       
       # 更新单元状态
       Ct = Ft * (cx + cell)
       
       # 计算新的隐藏状态
       Ht = Ft * torch.tanh(Ct)
       
       return Ht, (Ht, Ct)  # 返回输出和新的隐藏状态
   ```

这种设计结合了LSTM的记忆能力和Mamba的序列建模能力，能够有效捕获不同频率分量之间的关系，并在保持长期依赖的同时处理复杂的时频特征。

### 2.4.4 Mamba模块处理

Mamba是状态空间模型(SSM)的高效实现，它能捕获长距离依赖关系。MSB类封装了Mamba的功能，并添加了处理隐藏状态的能力：

```python
class MSB(MSSBlock):
    def __init__(
        self,
        hidden_dim: int = 160,    # 隐藏层维度
        drop_path: float = 0.159, # 随机路径丢弃率
        norm_layer: nn.Module = partial(nn.LayerNorm, eps=1e-6),
        d_state: int = 16,        # 状态空间维度
        d_conv: int = 4,          # 卷积核大小
        expand: int = 2,          # 扩展因子
        **kwargs
    ):
        super().__init__(
            hidden_dim=hidden_dim,
            drop_path=drop_path,
            norm_layer=norm_layer,
            d_state=d_state,
            d_conv=d_conv,
            expand=expand,
            **kwargs
        )
        
        # 处理隐藏状态的额外组件
        self.linear = nn.Linear(hidden_dim * 2, hidden_dim)
        self.hidden_dim = hidden_dim
        
    def forward(self, x, hx=None):
        B, T, C = x.shape  # 获取输入形状
        
        shortcut = x  # 残差连接
        x = self.ln_1(x)  # 层归一化
        
        if hx is not None:  # 如果提供了隐藏状态
            hx = self.ln_1(hx)  # 对隐藏状态进行归一化
            # 拼接输入和隐藏状态
            x = torch.cat((x, hx), dim=-1)
            # 投影回原始维度
            x = self.linear(x)
            
        # 通过Mamba处理
        mamba_out = self.self_attention(x)
        
        # 应用dropout并添加残差连接
        x = self.dropout(mamba_out)
        x = shortcut + x
        
        return x
```

1. 内部处理流程:
   - 归一化输入: 应用LayerNorm稳定训练
   - 特征融合: 如果有隐藏状态，拼接输入和状态并投影
   - Mamba处理: 通过状态空间模型处理序列
   - 残差连接: 添加原始输入和处理后的特征

2. Mamba状态空间模型的优势:
   - 线性复杂度(O(n))而非Transformer的二次复杂度(O(n²))
   - 能够处理较长序列而不会导致计算爆炸
   - 高效的序列建模能力，类似于RNN但计算更高效
   - 通过选择性记忆机制保留重要信息

### 2.4.5 最终分类过程

处理完所有分量后，MMRNNClassifier的后续步骤：

```python
# 取最后一个分量的输出（低频分量）
final_output = outputs[-1]  # [B, 64, 160]

# 平均池化
final_output = final_output.mean(dim=1)  # [B, 160]

# 分类头
self.classifier = nn.Sequential(
    nn.LayerNorm(hidden_dim),  # 先对特征进行归一化
    nn.Linear(hidden_dim, num_classes)  # 然后映射到类别数量
)
output = self.classifier(final_output)  # [B, 11]
```

1. 取最后一个分量(低频，c=2): [B, 64, 160]
   - 利用低频分量包含整体信号特征的特性
   - 低频分量已经通过前序分量的状态传递融合了高频信息
   - 这种设计类似于级联结构，信息从高频向低频流动

2. 平均池化: [B, 64, 160] -> [B, 160]
   - 在序列维度上进行平均池化，融合时间信息
   - 输出维度160由`hidden_dim`决定

3. 分类器处理:
   - 先应用LayerNorm归一化特征
   - 通过线性层映射到11个类别
   - 输出维度11由`num_classes`参数决定

## 3. num_levels参数与模型特性分析

### 3.1 num_levels=2 的模型特性

当 `num_levels=2` 时，模型具有以下特性：

1. **分量数量确定**:
   - 产生2个高频分量和1个低频分量，总共3个分量
   - `num_components = num_levels + 1 = 2 + 1 = 3`
   - 与`num_levels=3`相比减少了一个分量，计算效率更高

2. **序列长度变化**:
   - 原始序列长度为128
   - 高频分量1: 长度 = 128/2 = 64
   - 高频分量2: 长度 = 128/4 = 32
   - 低频分量: 长度 = 16 (与最后一级高频分量相同)
   - 对调制信号进行2级分解提供了足够的时频分辨率

3. **频率分析能力**:
   - 能够捕获2个不同尺度的高频特征
   - 高频分量1: 捕获最细粒度的高频变化（如相位跳变、符号边界）
   - 高频分量2: 捕获中等尺度的频率特征（如符号速率相关特征）
   - 低频分量: 捕获信号的整体趋势（如载波特性、能量分布）

4. **信息传递路径**:
   - 前序分量信息通过隐藏状态传递给后续分量
   - 信息流向: 高频1 → 高频2 → 低频
   - 分量间状态传递增强了频谱域的特征融合

5. **计算效率与性能平衡**:
   - 处理3个分量比处理4个分量(num_levels=3)计算量少约25%
   - 相比单级分解(num_levels=1)提供更丰富的频率域信息
   - 对于调制识别任务，2级分解通常能达到良好的性能和计算效率平衡

### 3.2 不同num_levels值的影响与选择

| num_levels | 分量数量 | 分辨率级别 | 计算复杂度 | 适用场景 |
|------------|---------|-----------|-----------|---------|
| 1 | 2 (1高频+1低频) | 低 | 低 | 简单调制方式，资源受限 |
| 2 | 3 (2高频+1低频) | 中 | 中 | 中等复杂度调制，一般场景 |
| 3 | 4 (3高频+1低频) | 高 | 中高 | 复杂调制方式区分 |
| 4 | 5 (4高频+1低频) | 更高 | 高 | 高复杂度调制，强干扰环境 |

选择`num_levels=2`的考虑因素：

1. **分辨率需求**：对于大多数调制方式，2级分解提供的时频分辨率已经足够区分不同的调制特征。

2. **计算效率**：增加`num_levels`值会线性增加计算复杂度，当`num_levels=2`时，可以在性能和效率之间取得良好的平衡。

3. **模型尺寸**：小波分解级数越多，模型参数也越多，`num_levels=2`产生的模型大小适中，便于部署。

4. **实验验证**：根据`config.yaml`配置，项目实验表明`num_levels=2`配合其他参数设置可以达到满意的识别准确率。

### 3.3 与其他关键参数的协同关系

`num_levels`参数需要与其他参数协同工作以达到最佳效果：

1. **与wavelet_dim的关系**：
   - 当`num_levels=2`时，`wavelet_dim=96`提供了足够的特征维度
   - 较高的`num_levels`值可能需要更大的`wavelet_dim`以捕获更丰富的特征

2. **与rnn_dim的关系**：
   - `rnn_dim=160`配合`num_levels=2`处理3个分量的隐藏状态
   - 分量数量增加时可能需要更大的`rnn_dim`以维持足够的表达能力

3. **与msb_depth的关系**：
   - `msb_depth=2`表示每个MMRNNCell中有2个MSB块
   - 较少的分量数(较小的`num_levels`)可通过增加`msb_depth`来补偿模型深度

## 4. 结论

WNN-MRNN模型通过结合小波分解和基于Mamba的递归网络，实现了对调制信号的高效分析和分类。其关键优势包括：

1. **多分辨率分析**：通过小波分解捕获不同频率范围的信号特征，为复杂调制信号提供全面的时频域表示。

2. **跨频率域信息融合**：通过MRNN架构在不同频率分量间传递信息，实现频率域之间的特征交互。

3. **高效序列处理**：利用Mamba状态空间模型代替传统RNN/LSTM，提高长序列处理效率，减少计算复杂度。

4. **可调节的复杂度**：通过`num_levels`参数平衡模型性能和计算资源，适应不同的应用场景。

5. **创新的小波学习机制**：使用神经网络学习优化的小波滤波器，而非传统的固定滤波器，增强模型适应性。

本文档详细分析了`num_levels=2`配置下的参数传递流程和数据维度变化，为模型的理解、调优和应用提供了全面参考。WNN-MRNN结构显示了在调制识别任务中的强大潜力，特别是在处理复杂信号环境时的鲁棒性。

## 4. 结论

WNN-MRNN模型通过结合小波分解和基于Mamba的递归网络，实现了对调制信号的高效分析和分类。其关键优势包括：

1. **多分辨率分析**：通过小波分解捕获不同频率范围的信号特征，为复杂调制信号提供全面的时频域表示。

2. **跨频率域信息融合**：通过MRNN架构在不同频率分量间传递信息，实现频率域之间的特征交互。

3. **高效序列处理**：利用Mamba状态空间模型代替传统RNN/LSTM，提高长序列处理效率，减少计算复杂度。

4. **可调节的复杂度**：通过`num_levels`参数平衡模型性能和计算资源，适应不同的应用场景。

5. **创新的小波学习机制**：使用神经网络学习优化的小波滤波器，而非传统的固定滤波器，增强模型适应性。

本文档详细分析了`num_levels=2`配置下的参数传递流程和数据维度变化，为模型的理解、调优和应用提供了全面参考。WNN-MRNN结构显示了在调制识别任务中的强大潜力，特别是在处理复杂信号环境时的鲁棒性。