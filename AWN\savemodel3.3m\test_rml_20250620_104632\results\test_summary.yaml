dataset_info:
  dataset_type: rml
  input_shape: !!python/tuple
  - 2
  - 128
  num_classes: 11
  snr_range:
  - -20.0
  - 18.0
  total_samples: 66000
inference_performance:
  avg_inference_time_ms: 0.041048226934490784
  max_inference_time_ms: 3.1993985176086426
  min_inference_time_ms: 0.03460049629211426
  std_inference_time_ms: 0.06053343071855932
model_complexity:
  macs: 157.040M
  macs_raw: 157040448.0
  parameters: 3.698M
  params_raw: 3697753.0
overall_metrics:
  accuracy: 61.04242424242424
  kappa: 0.5714666666666667
  macro_f1: 63.08587180365202
test_info:
  config_path: config.yaml
  model_path: ./saved_models/awn/rml_20250619_001205/models/best_model.pth
  test_date: '2025-06-20 10:47:02'
